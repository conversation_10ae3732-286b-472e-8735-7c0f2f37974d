import { type MergeV1MergeR<PERSON>ord, MergeV1FailReason, MergeV1MergeRecordOrderBy, MergeV1OrderDirection } from '@hg-tech/api-schema-merge';
import { BasicVxeTable } from '@hg-tech/oasis-common';
import { Avatar, Button, message, Pagination, Popconfirm, Popover, Tooltip } from 'ant-design-vue';
import dayjs from 'dayjs';
import { MergeV1FailReasonLabelMap, renderMergeV1FailReasonIcon } from '../../models/config.model';
import { type PropType, computed, defineComponent, ref, watch } from 'vue';
import type { VxeGridInstance, VxeGridListeners, VxeGridProps } from 'vxe-table';
import { OnlineResolver } from './online-resolver';
import { LocalResolver } from './local-resolver';
import { type UserTagInfo, UserTag } from '../../components/UserTag';
import Icon, { UserOutlined } from '@ant-design/icons-vue';
import { AssignResolver } from './assign-resolver';
import { SkipResolver } from './skip-resolver';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { mergeApi } from '../../api';
import { useMergeTask } from './use-merge-task';
import { useUserAuthStore } from '../../store/modules/userAuth';
import { store } from '../../store/pinia';
import { PermissionProvider } from '../../components/PermissionProvider';
import { MergePermission } from '../../constants/premission';
import { traceCustomEvent } from '../../services/track';
import { TrackEventName } from '../../constants/event';

import BasicStrokeAssign from '../../assets/svg/BasicStrokeAssign.svg?component';
import BasicStrokeSkip from '../../assets/svg/BasicStrokeSkip.svg?component';
import LogoStrokeSwarm from '../../assets/svg/LogoStrokeSwarm.svg?component';
import BasicStrokeReset from '../../assets/svg/BasicStrokeReset.svg?component';
import BasicStrokeConflict from '../../assets/svg/BasicStrokeConflict.svg?component';
import SystemStrokeTips from '../../assets/svg/SystemStrokeTips.svg?component';
import { ForgeonThemeCssVar } from '@hg-tech/forgeon-style';

const SortFieldMap: Partial<Record<string, MergeV1MergeRecordOrderBy>> = {
  cl: MergeV1MergeRecordOrderBy.MERGE_RECORD_ORDER_BY_CL,
  submitTime: MergeV1MergeRecordOrderBy.MERGE_RECORD_ORDER_BY_SUBMIT_TIME,
  handler: MergeV1MergeRecordOrderBy.MERGE_RECORD_ORDER_BY_HANDLER,
};

const TaskTable = defineComponent({
  props: {
    data: {
      type: Array as PropType<MergeV1MergeRecord[]>,
      default: () => [],
    },
    loading: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    page: {
      type: Number as PropType<number>,
      default: 1,
    },
    pageSize: {
      type: Number as PropType<number>,
      default: 20,
    },
    total: {
      type: Number as PropType<number>,
      default: 0,
    },
    onPageChange: {
      type: Function as PropType<(page: number, size: number) => void>,
    },
    onRefresh: {
      type: Function as PropType<() => void>,
    },
    onSortChange: {
      type: Function as PropType<(params: { field: MergeV1MergeRecordOrderBy; order: MergeV1OrderDirection }) => void>,
      default: () => {},
    },
  },
  emits: ['update:page', 'update:pageSize'],
  setup(props, { emit }) {
    const { currentProjectId, currentRuleId, currentBranchMap, ruleList } = useMergeTask();
    const { userProfile } = useUserAuthStore(store);
    const { execute: retryMerge } = useLatestPromise(mergeApi.v1.mergeServiceRetryMerge);
    const currentItem = ref<MergeV1MergeRecord>();
    const isShowOnlineResolver = ref(false);
    const isShowLocalResolver = ref(false);
    const isShowAssignResolver = ref(false);
    const isShowSkipResolver = ref(false);
    const page = computed({
      get: () => props.page,
      set: (value: number) => {
        emit('update:page', value);
      },
    });
    const pageSize = computed({
      get: () => props.pageSize,
      set: (value: number) => {
        emit('update:pageSize', value);
      },
    });
    const historyTableRef = ref<VxeGridInstance<MergeV1MergeRecord>>();
    const tableRows = computed(() => props.data);
    const currentRule = computed(() => ruleList.value.find((rule) => rule.id === currentRuleId.value));
    watch(() => props.data, () => {
      if (historyTableRef.value) {
        setTimeout(() => {
          historyTableRef.value?.scrollTo(0, 0);
        }, 0);
      }
    }, { deep: true });

    const onResolver = (row: MergeV1MergeRecord, type: 'online' | 'local' | 'assign' | 'skip') => {
      currentItem.value = row;
      isShowOnlineResolver.value = type === 'online';
      isShowLocalResolver.value = type === 'local';
      isShowAssignResolver.value = type === 'assign';
      isShowSkipResolver.value = type === 'skip';
    };

    const onCheckFiles = (row: MergeV1MergeRecord) => {
      traceCustomEvent(TrackEventName.conflux_go_to_swarm, {
        conflux_rule_id: currentRule.value?.id,
        conflux_source_branch_path: currentRule.value?.sourceStreamId ? currentBranchMap.value.get(currentRule.value?.sourceStreamId)?.path : undefined,
        conflux_target_branch_path: currentRule.value?.targetStreamId ? currentBranchMap.value.get(currentRule.value?.targetStreamId)?.path : undefined,
      });
      if (!row.swarmAddress) {
        message.warning('该记录没有提交文件地址');
        return;
      }
      window.open(row.swarmAddress, '_blank');
    };

    const onRetry = async (row: MergeV1MergeRecord) => {
      traceCustomEvent(TrackEventName.conflux_failed_merge_retry, {
        conflux_rule_id: currentRule.value?.id,
        conflux_source_branch_path: currentRule.value?.sourceStreamId ? currentBranchMap.value.get(currentRule.value?.sourceStreamId)?.path : undefined,
        conflux_target_branch_path: currentRule.value?.targetStreamId ? currentBranchMap.value.get(currentRule.value?.targetStreamId)?.path : undefined,
      });
      await retryMerge({ id: currentProjectId.value! }, {
        recordId: row.id,
      });
      // 触发父组件的刷新操作
      props.onRefresh?.();
      message.success('重试成功');
    };

    const renderFailReason = (row: MergeV1MergeRecord) => {
      return (
        <div class="flex items-center justify-center gap-4px">
          {row.failReason && renderMergeV1FailReasonIcon[row.failReason]?.()}
          {row.failReason && <span>{MergeV1FailReasonLabelMap[row.failReason] || '未知错误'}</span>}
          {row.failDetail && (
            <Popover placement="topRight">
              {{
                default: () => (<Icon class="c-FO-Content-Icon2" component={<SystemStrokeTips />} />),
                content: () => (
                  <>
                    <div class="FO-Font-R14 mb-8px flex items-center gap-8px c-FO-Content-Text1">
                      {renderMergeV1FailReasonIcon[row.failReason as MergeV1FailReason]?.()}
                      <span>{MergeV1FailReasonLabelMap[row.failReason as MergeV1FailReason] || '未知错误'}</span>
                    </div>
                    <div class="FO-Font-R12 max-h-200px max-w-500px overflow-y-auto whitespace-pre-wrap break-words pl-24px c-FO-Content-Text2 line-height-[16px]">
                      <div>{row.failDetail || '暂无详细信息'}</div>
                    </div>
                  </>
                ),
              }}
            </Popover>
          )}
        </div>
      );
    };

    const tableColumns = computed<VxeGridProps<MergeV1MergeRecord>['columns']>(() => [
      { type: null, width: 40, resizable: false },
      { field: 'cl', title: 'CL号', width: 100, sortable: true },
      { field: 'submitTime', title: '提交时间', width: 180, sortable: true, slots: {
        default({ row }) {
          return row.submitTime ? dayjs(Number(row.submitTime) * 1000).format('YYYY-MM-DD HH:mm:ss') : '';
        },
      } },
      { field: 'description', title: '提交描述', minWidth: 200, showOverflow: 'tooltip', slots: {
        default({ row }) {
          return row.description || '--';
        },
      } },
      { field: 'handler', title: '当前处理人', sortable: true, width: 180, slots: {
        default({ row }) {
          if (!row.handler || row.handler.length === 0) {
            return '无';
          }
          if (row.handler.length === 1) {
            const user: UserTagInfo = {
              openId: row.handler[0].feishuOpenId || '',
              name: row.handler[0].name || row.handler[0].hgAccount || '--',
              avatar: row.handler[0].avatar || '',
              nickname: row.handler[0].nickname || '',
            };
            return <UserTag avatarSize={18} user={user} />;
          }
          return (
            <Avatar.Group class="cursor-pointer">
              {row.handler?.slice(0, 3).map((user) => (
                <Popover placement="top" z-index={2000}>{{
                  default: () => (
                    <Avatar icon={user.avatar ? undefined : <UserOutlined />} size={22} src={user.avatar} />
                  ),
                  content: () => (
                    <div class="flex flex-col gap-4px">
                      <UserTag
                        avatarSize={18}
                        user={{
                          openId: user.feishuOpenId || '',
                          name: user.name || '',
                          avatar: user.avatar || '',
                          nickname: user.nickname || '',
                        }}
                      />
                    </div>
                  ),
                }}
                </Popover>
              ))}
              {row.handler && row.handler.length > 3 && (
                <Popover placement="top" z-index={2000}>
                  {{
                    default: () => (
                      <Avatar class="bg-fill-gray1-blank-active c-FO-Content-Text2" size={24}>
                        {`+${row.handler?.length ? row.handler.length - 3 : 0}`}
                      </Avatar>
                    ),
                    content: () => (
                      <div class="flex flex-col gap-4px">
                        {row.handler?.slice(3).map((user) => {
                          const userInfo: UserTagInfo = {
                            openId: user.feishuOpenId || '',
                            name: user.name || user.hgAccount || '',
                            avatar: user.avatar || '',
                            nickname: user.nickname || '',
                          };
                          return <UserTag avatarSize={24} user={userInfo} />;
                        })}
                      </div>
                    ),
                  }}
                </Popover>
              )}
            </Avatar.Group>
          );
        },
      } },
      { field: 'failReason', title: '失败原因', align: 'center', width: 120, slots: {
        default({ row }) {
          return renderFailReason(row);
        },
      } },
      {
        field: 'actions',
        title: '操作',
        width: 400,
        fixed: 'right',
        minWidth: 400,
        slots: {
          default({ row }) {
            const isShowOnlineResolver = row.failReason === MergeV1FailReason.FAIL_REASON_CONFLICT;
            const isShowLocalResolver = row.failReason && [
              MergeV1FailReason.FAIL_REASON_COMMIT_FAILED,
              MergeV1FailReason.FAIL_REASON_CONFLICT,
            ].includes(row.failReason);
            // 判断当前用户是否是处理人
            const isHandler = Boolean(
              row.handler
              && row.handler.length > 0
              && row.handler?.some((user) => user.hgAccount === userProfile?.userName),
            );
            return (
              <div class="flex items-center gap-4px">
                <PermissionProvider customCheck={() => isHandler && !!row.onlineProcessSwitch} permission={{ any: [MergePermission.ResolveOnline] }}>
                  {isShowOnlineResolver && (
                    <Tooltip placement="top" title="在线处理">
                      <Button
                        class="btn-fill-default"
                        icon={<Icon class="font-size-16px" component={<BasicStrokeConflict />} />}
                        onClick={() => onResolver(row, 'online')}
                        type="text"
                      >
                        在线处理
                      </Button>
                    </Tooltip>
                  )}
                </PermissionProvider>
                <PermissionProvider customCheck={() => isHandler} permission={{ any: [MergePermission.ResolveLocal] }}>
                  {isShowLocalResolver && (
                    <Tooltip placement="top" title="本地处理">
                      <Button
                        class="btn-fill-default"
                        icon={<Icon class="font-size-16px" component={<BasicStrokeConflict />} />}
                        onClick={() => onResolver(row, 'local')}
                        type="text"
                      >
                        本地处理
                      </Button>
                    </Tooltip>
                  )}
                </PermissionProvider>
                <PermissionProvider customCheck={() => isHandler} permission={{ any: [MergePermission.Assign] }}>
                  <Tooltip placement="top" title="分配">
                    <Button class="btn-fill-text" icon={<Icon class="font-size-14px c-FO-Content-Icon1" component={<BasicStrokeAssign />} />} onClick={() => onResolver(row, 'assign')} type="text" />
                  </Tooltip>
                </PermissionProvider>
                <PermissionProvider customCheck={() => isHandler} permission={{ any: [MergePermission.Skip] }}>
                  <Tooltip placement="top" title="跳过">
                    <Button class="btn-fill-text" icon={<Icon class="font-size-14px c-FO-Content-Icon1" component={<BasicStrokeSkip />} />} onClick={() => onResolver(row, 'skip')} type="text" />
                  </Tooltip>
                </PermissionProvider>
                <PermissionProvider customCheck={() => isHandler} permission={{ any: [MergePermission.ShowSwarm] }}>
                  <Tooltip placement="top" title="查看提交文件">
                    <Button class="btn-fill-text" icon={<Icon class="font-size-14px c-FO-Content-Icon1" component={<LogoStrokeSwarm />} />} onClick={() => onCheckFiles(row)} type="text" />
                  </Tooltip>
                </PermissionProvider>
                <PermissionProvider customCheck={() => isHandler} permission={{ any: [MergePermission.Retry] }}>
                  <Tooltip placement="top" title="重试">
                    <Popconfirm cancelText="取消" okText="确认" onConfirm={() => onRetry(row)} placement="bottomRight">
                      {{
                        default: () => (
                          <Button class="btn-fill-text" icon={<Icon class="font-size-14px c-FO-Content-Icon1" component={<BasicStrokeReset />} />} type="text" />
                        ),
                        title: () => (
                          <div class="w-200px c-FO-Content-Text2">
                            确认要重试当前CL的合并任务吗？
                          </div>
                        ),
                      }}
                    </Popconfirm>
                  </Tooltip>
                </PermissionProvider>
              </div>
            );
          },
        },
      },
    ]);

    const gridOptions = computed(() => ({
      rowConfig: {
        keyField: 'id',
        isHover: true,
      },
      sortConfig: {
        remote: true,
      },
      height: '100%',
      loading: props.loading,
      columns: tableColumns.value,
      data: tableRows.value,
    }) as VxeGridProps);

    const gridEvent: VxeGridListeners<MergeV1MergeRecord> = ({
      sortChange({ field, order }) {
        props.onSortChange({
          field: SortFieldMap[field] || MergeV1MergeRecordOrderBy.MERGE_RECORD_ORDER_BY_INVALID,
          order: order === 'asc' ? MergeV1OrderDirection.ORDER_DIRECTION_ASC : MergeV1OrderDirection.ORDER_DIRECTION_DESC,
        });
      },
    });

    return () => (
      <div class="task-table flex flex-1 flex-col overflow-hidden">
        <div class="mb-24px flex-1">
          <BasicVxeTable
            cssVarOverrides={{
              '--vxe-ui-layout-background-color': ForgeonThemeCssVar.ContainerFill1,
            }}
            events={gridEvent}
            options={gridOptions.value}
            ref={historyTableRef}
          />
        </div>
        <div class="mb-12px flex items-center justify-end">
          <Pagination
            onChange={props.onPageChange}
            showSizeChanger
            total={props.total}
            v-model:current={page.value}
            v-model:pageSize={pageSize.value}
          />
        </div>
        <OnlineResolver
          data={currentItem.value}
          v-model:visible={isShowOnlineResolver.value}
        />
        <LocalResolver
          data={currentItem.value}
          v-model:visible={isShowLocalResolver.value}
        />
        <AssignResolver
          data={currentItem.value}
          v-model:visible={isShowAssignResolver.value}
        />
        <SkipResolver
          data={currentItem.value}
          v-model:visible={isShowSkipResolver.value}
        />
      </div>
    );
  },
});

export {
  TaskTable,
};
