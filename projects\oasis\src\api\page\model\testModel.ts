import type { BaseItem, BasicFetchResult, BasicPageParams, BasicResult } from '/@/api/model/baseModel';
import type { ProjectListItem, ShortURLItem } from '/@/api/page/model/systemModel';
import type { DevicePlatform } from '../../../views/toolkit/settings/toolkitSettings.data.ts';

/** 游戏存档列表 Model */
export interface GameArchivesListItem extends BaseItem {
  name?: string;
  branch?: number;
  dataURL?: string;
  projectID?: number;
}

/** id获取游戏存档信息 接口参数 */
export interface GameArchivesItemParams {
  rearchive: GameArchivesListItem;
}

/** 批量复制游戏存档信息 接口参数 */
export interface CopyGameArchivesParams {
  srcBranchID: number;
  dstBranchID: number;
  idList: number[];
}

/** 批量删除游戏存档信息 接口参数 */
export interface BatchDeleteGameArchivesParams {
  ids: number[];
}

/** 游戏存档列表 接口参数 */
export type GameArchivesPageParams = BasicPageParams & GameArchivesListItem;

/** 游戏存档列表 接口返回数据 */
export type GameArchivesListGetResultModel = BasicFetchResult<GameArchivesListItem>;

/** 游戏分支列表 Model */
export interface GameBranchesListItem extends BaseItem {
  name?: string;
  editable?: boolean;
  isNew?: boolean;
}

/** id获取游戏分支信息 接口参数 */
export interface GameBranchesItemParams {
  rebranch: GameBranchesListItem;
}

/** 游戏分支列表 接口参数 */
export type GameBranchesPageParams = BasicPageParams & GameBranchesListItem;

/** 游戏分支列表 接口返回数据 */
export type GameBranchesListGetResultModel = BasicFetchResult<GameBranchesListItem>;

export interface GamePackagesListNotifyPrivateItem {
  groups?: string[];
  memberIDs?: string[];
}

/** 项目游戏包通知条目 Model */
export interface GamePackagesListNotifyItem {
  receiverType?: 1 | 2; // 1: 群聊，2: 私聊
  chatIDs?: string[];
  private?: GamePackagesListNotifyPrivateItem;
  robotKey?: string;
}

/** 项目游戏包通知 Model */
export interface GamePackagesListNotify {
  version?: GamePackagesListNotifyItem;
  doctor?: GamePackagesListNotifyItem;
}
/** 项目游戏包列表 Model */
export interface GamePackagesListItem extends BaseItem {
  name?: string;
  icon?: string;
  projectID?: number;
  project?: ProjectListItem;
  platforms?: number[];
  bundleID?: string;
  applicationId?: number;
  visible: 'enable' | 'disable';
  visibleMembers?: number[];
  visibleGroups?: string[];
  notify?: GamePackagesListNotify;
  notifyV2?: GamePackagesListNotify;
  notifyV3?: GamePackagesListNotify;
  briefName?: string;
}

/** id获取游戏包信息 接口参数 */
export interface GamePackageItemParams {
  regamePkg: GamePackagesListItem;
}

/** 项目游戏包列表 接口参数 */
export type GamePackagesPageParams = BasicPageParams & GamePackagesListItem;

/** 项目游戏包列表 接口返回数据 */
export type GamePackagesListGetResultModel = BasicFetchResult<GamePackagesListItem>;

/** 游戏包版本标记列表 Model */
export interface GamePackagesVersionMarksListItem {
  name?: string;
  color?: string;
  UUID?: number;
}
export interface extLinksItem {
  title: string;
  URL: string;
  ID?: number;
  isOpen?: boolean;
}
/** 项目游戏包版本列表 Model */
export interface GamePackagesVersionsListItem extends BaseItem {
  version?: string;
  pkgID?: number;
  /** 1 表示 Android，2 表示 IOS */
  platform?: DevicePlatform;
  /** 包文件 */
  pkgFile?: string;
  /** 下载地址 */
  downloadLink?: string;
  /** 是否长期保留 */
  reserve?: boolean;
  icon?: string;
  releaseNote?: string;
  /** 包体检测ID */
  doctorID?: number;
  /** 下载地址短连接 */
  shortURL?: ShortURLItem;
  /** 是否置顶 */
  top?: boolean;
  sizeKB?: number;
  passDoctor?: boolean;
  labels?: GameLabelsListItem[];
  marks?: GamePackagesVersionMarksListItem[];
  lbs?: string[];
  attachments?: GameAttachmentsListItem[];
  unfinished?: boolean;
  /** 过期天数 */
  remainDays?: number;

  /** 前端用 */
  checked?: boolean;
  color?: number;
  colors?: number[];
  extLinks?: extLinksItem[];
}

/** id获取游戏包版本信息 接口参数 */
export interface GamePackageVersionItemParams {
  repkgVersion: GamePackagesVersionsListItem;
  reVersion?: GamePackagesVersionsListItem;
}

/** 项目游戏包版本列表 接口参数 */
export type GamePackagesVersionsPageParams = BasicPageParams & GamePackagesVersionsListItem;
/** 项目游戏包分类 接口参数 */
export type PkgClassListPageParams = BasicPageParams & BasicFetchResult<PkgClassListItem>;
export type PkgClassWithpkgListPageParams = BasicPageParams & BasicFetchResult<PkgClassWithpkgListItem>;
/** 项目游戏包版本列表 接口返回数据 */
export type GamePackagesVersionsListGetResultModel = BasicFetchResult<GamePackagesVersionsListItem> & { versionTotal?: number };
export type setPkgClassListResultModel = BasicResult & { createdIds: number[]; updatedIds: number[]; deletedIds: number[] };
export interface GamePackagesVersionListSearchParamsModel {
  platform: number | undefined;
  doctor: boolean | undefined;
  labels: Record<number, number | number[]>;
  colors?: number[];
  color?: number[];
}

export interface PkgClassListItem extends BaseItem {
  game_packages: any;
  is_default: boolean;
  name: string;
  project_id: number;
  sort: number;
}
export interface PkgClassWithpkgListItem extends BaseItem {
  game_packages: GamePackagesListItem[];
  is_default: false;
  name: string;
  project_id: number;
  sort: number;
}

/** 项目打包信息列表 Model */
export interface ProjectPkgBuildInfoListItem extends BaseItem {
  versionID?: number;
  engineCL?: number;
  shelveCL?: number;
  projectCL?: number;
  trigger?: string;
  color?: number;
}
export interface ProjectPkgBuildInfoCLDetailItem {
  desc: string;
  time: string;
  user: string;
}
export interface ProjectPkgBuildInfoCLDetailParams {
  pkgID: number;
  /** 1：项目CL或引擎CL，2：Shelve CL */
  clType: number;
  cl: number;
}
/** 项目打包信息列表 接口参数 */
export type ProjectPkgBuildInfoListPageParams = BasicPageParams & ProjectPkgBuildInfoListItem;

/** 项目打包信息列表 接口返回数据 */
export type ProjectPkgBuildInfoListGetResultModel = BasicFetchResult<ProjectPkgBuildInfoListItem>;

/** 游戏包体检测详情条目 Model */
export interface GamePackageDoctorDetailItem {
  container?: string;
  containers?: string[];
  dimension?: string;
  duplicate?: number;
  format?: string;
  name?: string;
  names?: string[];
  preview?: string;
  size?: string;
  size_raw?: number;
  origin_files?: string[];
  type?: string;
  wasted?: string;
  wasted_raw?: number;
  preItem?: GamePackageDoctorDetailItem;
  dataList?: GamePackageDoctorDetailItem[];

}

/** 游戏包体检测详情汇总条目 Model */
export interface GamePackageDoctorDetailSummaryItem {
  name: string;
  total: string;
  total_percent: string;
  total_raw: number;
  wasted: string;
  wasted_percent: string;
  wasted_raw: number;
}

/** 游戏包体检测详情汇总 Model */
export interface GamePackageDoctorDetailSummary {
  list: GamePackageDoctorDetailSummaryItem[];
  total_bytes: string;
  total_bytes_raw: number;
  wasted_bytes: string;
  wasted_bytes_raw: number;
}

/** 游戏包体检测详情汇总未压缩条目 Model */
export interface GamePackageDoctorDetailSummaryUncompressedItem {
  name: string;
  reduce: string;
}

/** 游戏包体检测详情汇总未压缩 Model */
export interface GamePackageDoctorDetailSummaryUncompressed {
  list: GamePackageDoctorDetailSummaryUncompressedItem[];
  uncompressed_bytes: string;
  uncompressed_bytes_raw: number;
}

/** 游戏包体检测UI Panel关联图集子集 Model */
export interface GamePackageDoctorDetailUIPanelElementItem {
  name?: string;
  type?: string;
  size?: string;
  size_raw?: number;
  dimension?: string;
  format?: string;
  preview?: string;
  origin_file?: string;
  preItem?: GamePackageDoctorDetailUIPanelElementItem;
  dataList?: GamePackageDoctorDetailUIPanelElementItem[];
}

/** 游戏包体检测UI Panel关联图集 Model */
export interface GamePackageDoctorDetailUIPanelItem {
  container?: string;
  size?: string;
  size_raw?: number;
  preItem?: GamePackageDoctorDetailItem;
  elements?: GamePackageDoctorDetailUIPanelElementItem[];
  dataList?: GamePackageDoctorDetailUIPanelItem[];
  containers?: string[];
}

/** 游戏包体检测UI Panel关联图集 Top榜 Model */
export interface GamePackageDoctorDetailPrefab {
  ui_panel: GamePackageDoctorDetailUIPanelItem[];
}

/** 游戏包体检测详情 Model */
export interface GamePackageDoctorDetail extends BaseItem {
  big_size: GamePackageDoctorDetailItem[];
  duplicate: GamePackageDoctorDetailItem[];
  uncompressed: GamePackageDoctorDetailItem[];
  solid_color_image: GamePackageDoctorDetailItem[];
  large_mesh: GamePackageDoctorDetailItem[];
  large_animation: GamePackageDoctorDetailItem[];
  summary?: GamePackageDoctorDetailSummary;
  summary_uncompressed?: GamePackageDoctorDetailSummaryUncompressed;
  doctorUUID?: string;
  containers?: string[];
  prefab: GamePackageDoctorDetailPrefab;
  version?: string;
}

/** 游戏包体检测列表 Model */
export interface GamePackageDoctorListItem extends BaseItem {
  doctor?: GamePackageDoctorDetail;
  pkgID?: number;
  platform?: number;
  version?: GamePackagesVersionsListItem;
  game?: GamePackagesListItem;
  UUID?: number;
}

/** 游戏包体检测列表 接口参数 */
export type GamePackageDoctorListPageParams = BasicPageParams & GamePackageDoctorListItem;

/** 游戏包体检测列表 接口返回数据 */
export type GamePackagesDoctorListGetResultModel = BasicFetchResult<GamePackageDoctorListItem>;

/** id获取游戏包体检测信息 接口返回值 */
export interface GamePackagesDoctorItemGetResultModel {
  packageDoctor: GamePackageDoctorListItem;
}

/** 游戏包体检测趋势图 接口参数 */
export interface GamePackagesDoctorTrendParams {
  idList?: number[];
}

/** id获取游戏包体检测趋势图信息 接口返回值 */
export interface GamePackagesDoctorTrendGetResultModel {
  list: GamePackageDoctorDetail[];
}

/** 游戏标签列表 Model */
export interface GameLabelsListItem extends BaseItem {
  name?: string;
  identifier?: string;
  singleChoice?: boolean;
  color?: string;
  values?: GameLabelValuesListItem[];
  sort?: number;
  popoverVisible?: boolean;
  /** 前端用 */
  UUID?: number;
  hasNewValueEdit?: boolean;
}

/** id获取游戏标签信息 接口参数 */
export interface GameLabelsItemParams {
  label: GameLabelsListItem;
}

/** 游戏标签列表 接口参数 */
export type GameLabelsPageParams = BasicPageParams & GameLabelsListItem;

/** 游戏标签列表 接口返回数据 */
export type GameLabelsListGetResultModel = BasicFetchResult<GameLabelsListItem>;

/** 游戏标签取值列表 Model */
export interface GameLabelValuesListItem extends BaseItem {
  labelID?: number;
  value?: string;
  checked?: boolean;
  valueID?: number;
  UUID?: number;
}

/** id获取游戏标签取值信息 接口参数 */
export interface GameLabelValuesItemParams {
  labelValue: GameLabelValuesListItem;
}

/** 游戏标签取值列表 接口参数 */
export type GameLabelValuesPageParams = BasicPageParams & GameLabelValuesListItem;

/** 游戏标签取值列表 接口返回数据 */
export type GameLabelValuesListGetResultModel = BasicFetchResult<GameLabelValuesListItem>;

/** 批量给包体标签排序接口参数 Model */
export interface BatchGameLabelSortParams {
  idList: number[];
}

/** 游戏附件列表 Model */
export interface GameAttachmentsListItem extends BaseItem {
  versionID?: number;
  name?: string;
  downloadLink?: string;
}

/** id获取游戏附件信息 接口参数 */
export interface GameAttachmentsItemParams {
  attachment: GameAttachmentsListItem;
}

/** 游戏附件列表 接口参数 */
export type GameAttachmentsPageParams = BasicPageParams & GameAttachmentsListItem;

/** 游戏附件列表 接口返回数据 */
export type GameAttachmentsListGetResultModel = BasicFetchResult<GameAttachmentsListItem>;

/** 游戏包清理规则标签规则列表 Model */
export interface GameCleanRulesLabelRuleItem {
  /** 标签值ID */
  labelValueID?: number;
  /** 保留天数 */
  days: number;

  /** 前端用 */
  isEdit?: boolean;
}
export interface GameTestConfigItem {
  pkgID?: number;
  projectID?: number;
  rule: number;
  path: string[];
}

export interface GameCleanRulesCustomRuleItem {
  labelRules: GameCleanRulesLabelRuleItem[];
  defaultDays: number;
}
/** 游戏包清理规则列表 Model */
export interface GameCleanRulesListItem extends BaseItem {
  /** 是否使用默认删除规则 */
  useDefault: boolean;
  /** 默认保留天数 */
  defaultDays: number;
  /** 自定义规则 */
  customRule: GameCleanRulesCustomRuleItem;
}

/** id获取游戏包清理规则信息 接口参数 */
export interface GameCleanRulesItemParams {
  recleanRule: GameCleanRulesListItem;
}
/** 游戏包清理规则列表 接口参数 */
export type GameCleanRulesPageParams = BasicPageParams;

/** 游戏包清理规则列表 接口返回数据 */
export type GameCleanRulesListGetResultModel = BasicFetchResult<GameCleanRulesListItem>;

/** 项目游戏包默认筛选列表 Model */
export interface GamePackagesFiltersListItem extends BaseItem {
  platform: {
    values: number[];
  };
  doctor: {
    values: number[];
  };
  label: {
    filters: [
      {
        label_id: number;
        values: number[];
      },
    ];
  };
  color: {
    values: number[];
  };
}

/** id获取游戏包默认筛选信息 接口参数 */
export interface GamePackageFilterItemParams {
  pkgFilter: GamePackagesFiltersListItem;
}

/** 项目游戏包默认筛选列表 接口参数 */
export type GamePackagesFiltersPageParams = BasicPageParams;

/** 项目游戏包默认筛选列表 接口返回数据 */
export type GamePackagesFiltersListGetResultModel = BasicFetchResult<GamePackagesFiltersListItem>;

/** 场景分支列表 Model */
export interface SceneBranchListItem {
  branchList: GamePackagesListItem[];
}

/** 场景分支列表 接口返回数据 */
export type SceneBranchListGetResultModel = SceneBranchListItem;

/** 日志源 */
export enum LogSource {
  Web = 1,
  Oasis = 2,
}

/** 包体中心埋点触发渠道 */
export enum GamePackageCenterTrackTrigger {
  Web = 'web',
  App = 'app',
  Oasis = 'oasis',
}

/** 包体中心埋点操作类型 */
export enum GamePackageCenterTrackOperation {
  Page = 'page',
  Oasis = 'oasis',
  CloudGame = 'cloud_game',
  CloudDevice = 'cloud_device',
  CI = 'ci',
}

/** 包体中心埋点页面来源 */
export enum GamePackageCenterTrackSourceType {
  DetailPage = 'detail_page',
  ListItem = 'list_item',
}

/** 包体中心埋点查询参数 */
export interface GamePackageCenterTrackQuery {
  /** 触发渠道 */
  trigger: GamePackageCenterTrackTrigger;
  /** 触发执行类型 */
  operation: GamePackageCenterTrackOperation;
  /** 是否部署云游戏 */
  deploy?: boolean;
}

export enum CloudGameDeployStatusEnum {
  NotDeployed = 0,
  Deploying = 1,
  Deployed = 2,
  DeployFailed = 3,
}
export interface CloudGameDeployStatus {
  status: CloudGameDeployStatusEnum;
  versionId: number;
  visible: boolean;
}
