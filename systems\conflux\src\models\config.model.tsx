import {
  type RuleV1Additional<PERSON>and<PERSON>,
  type RuleV1FeishuItemValue,
  type RuleV1FileNameRule,
  type UserUser,
  ApiNotifyV1NotifyScheduleType,
  MergeV1FailReason,
  MergeV1MergeState,
  MergeV1OperationType,
  MergeV1ResolveRule,
  RuleV1AdditionalHandlerField,
  RuleV1ExcludeMergeType,
  RuleV1FeishuItemOperation,
  RuleV1FileNameRuleType,
  RuleV1HandlerOperation,
  RuleV1MergeTrigger,
  RuleV1OnlineProcessFileType,
  RuleV1ResolveRule,
  RuleV1StreamType,
} from '@hg-tech/api-schema-merge';
import StreamNodeDev from '../assets/image/StreamNodeDev.png';
import StreamNodeRelease from '../assets/image/StreamNodeRelease.png';
import StreamNodeMain from '../assets/image/StreamNodeMain.png';
import BasicFillCheck2 from '../assets/svg/BasicFillCheck2.svg?component';
import BasicFillBlock from '../assets/svg/BasicFillBlock.svg?component';
import BasicFillWarning from '../assets/svg/BasicFillWarning.svg?component';
import BasicStrokeMerge from '../assets/svg/BasicStrokeMerge.svg?component';
import BasicStrokeTag from '../assets/svg/BasicStrokeTag.svg?component';
import SafeResolveArrow from '../assets/svg/SaveResolveArrow.svg?component';
import SafeMergedArrow from '../assets/svg/SaveMergedArrow.svg?component';
import AcceptSourceArrow from '../assets/svg/AcceptSourceArrow.svg?component';
import AcceptTargetArrow from '../assets/svg/AcceptTargetArrow.svg?component';
import BasicStrokeClock from '../assets/svg/BasicStrokeClock.svg?component';
import BasicStrokeProcessing from '../assets/svg/BasicStrokeProcessing.svg?component';
import Subtract from '../assets/svg/Subtract.svg?component';
import BasicStrokeAssign from '../assets/svg/BasicStrokeAssign.svg?component';
import BasicStrokeSkip from '../assets/svg/BasicStrokeSkip.svg?component';
import BasicStrokeReset from '../assets/svg/BasicStrokeReset.svg?component';
import BasicStrokeConflict from '../assets/svg/BasicStrokeConflict.svg?component';
import BasicStrokeLoader from '../assets/svg/BasicStrokeLoader.svg?component';
import Close from '../assets/svg/Close.svg?component';

import Icon from '@ant-design/icons-vue';
import type { JSX } from 'vue/jsx-runtime';

enum MergeRuleFormPanelKey {
  Default = 'default',
  Advanced = 'advanced',
}

const TriggerTypeLabelMap: Record<RuleV1MergeTrigger, string> = {
  [RuleV1MergeTrigger.AUTO]: '自动触发(auto)',
  [RuleV1MergeTrigger.TAG]: '标签触发(tag)',
  [RuleV1MergeTrigger.API]: 'API触发(api)',
  [RuleV1MergeTrigger.MERGE_TRIGGER_INVALID]: '未知触发器',
};

const TriggerTypeOptions: Array<{ label: string; value: RuleV1MergeTrigger; desc: string }> = [
  {
    label: TriggerTypeLabelMap[RuleV1MergeTrigger.AUTO],
    value: RuleV1MergeTrigger.AUTO,
    desc: '源分支的每一条提交都会自动尝试合并到目标分支（除非被排除）',
  },
  {
    label: TriggerTypeLabelMap[RuleV1MergeTrigger.TAG],
    value: RuleV1MergeTrigger.TAG,
    desc: '只有当源分支检测到特定标签的时候才会合并到目标分支（除非被排除）',

  },
  // {
  //   label: TriggerTypeLabelMap[TriggerType.Api],
  //   value: TriggerType.Api,
  //   desc: '只有用 API 请求的时候才会合并到目标分支 (除非被排除)',
  // },
];

const MergeRuleLabelMap: Record<RuleV1ResolveRule, string> = {
  [RuleV1ResolveRule.SAFE_RESOLVE_NO_MERGE]: 'Safe Resolve (No Merge)',
  [RuleV1ResolveRule.SAFE_RESOLVE_ALLOW_MERGE]: 'Safe Resolve (Allow Merge)',
  [RuleV1ResolveRule.ACCEPT_SOURCE]: 'Accept Source',
  [RuleV1ResolveRule.ACCEPT_TARGET]: 'Accept Target',
  [RuleV1ResolveRule.RESOLVE_RULE_INVALID]: 'Invalid Rule',
  [RuleV1ResolveRule.UNITY_YAML_MERGE]: 'Unity YAML Merge',
};

const MergeRuleOptions: Array<{ label: string; value: RuleV1ResolveRule; desc: string }> = [
  {
    label: MergeRuleLabelMap[RuleV1ResolveRule.SAFE_RESOLVE_NO_MERGE],
    value: RuleV1ResolveRule.SAFE_RESOLVE_NO_MERGE,
    desc: '提交分支和目标分支合并的文件出现冲突时，直接放弃解决并发送失败的通知',
  },
  {
    label: MergeRuleLabelMap[RuleV1ResolveRule.SAFE_RESOLVE_ALLOW_MERGE],
    value: RuleV1ResolveRule.SAFE_RESOLVE_ALLOW_MERGE,
    desc: '提交分支和目标分支合并的文件出现冲突时，若两个文件修改的地方不同，接收合并的结果；若合并失败，则发送通知。',
  },
  {
    label: MergeRuleLabelMap[RuleV1ResolveRule.ACCEPT_SOURCE],
    value: RuleV1ResolveRule.ACCEPT_SOURCE,
    desc: '提交分支和目标分支合并的文件出现冲突时，由提交分支的文件直接覆盖。',
  },
  {
    label: MergeRuleLabelMap[RuleV1ResolveRule.ACCEPT_TARGET],
    value: RuleV1ResolveRule.ACCEPT_TARGET,
    desc: '提交分支和目标分支合并的文件出现冲突时，放弃合并且保留目标分支的修改。',
  },
];

const CustomMergeRuleOptions = [{
  label: MergeRuleLabelMap[RuleV1ResolveRule.SAFE_RESOLVE_NO_MERGE],
  value: RuleV1ResolveRule.SAFE_RESOLVE_NO_MERGE,
  desc: '提交分支和目标分支合并的文件出现冲突时，直接放弃解决并发送失败的通知',
}, {
  label: MergeRuleLabelMap[RuleV1ResolveRule.SAFE_RESOLVE_ALLOW_MERGE],
  value: RuleV1ResolveRule.SAFE_RESOLVE_ALLOW_MERGE,
  desc: '提交分支和目标分支合并的文件出现冲突时，若两个文件修改的地方不同，接收合并的结果；若合并失败，则发送通知。',
}, {
  label: MergeRuleLabelMap[RuleV1ResolveRule.ACCEPT_SOURCE],
  value: RuleV1ResolveRule.ACCEPT_SOURCE,
  desc: '提交分支和目标分支合并的文件出现冲突时，由提交分支的文件直接覆盖。',
}, {
  label: MergeRuleLabelMap[RuleV1ResolveRule.ACCEPT_TARGET],
  value: RuleV1ResolveRule.ACCEPT_TARGET,
  desc: '提交分支和目标分支合并的文件出现冲突时，放弃合并且保留目标分支的修改。',
}, {
  label: MergeRuleLabelMap[RuleV1ResolveRule.UNITY_YAML_MERGE],
  value: RuleV1ResolveRule.UNITY_YAML_MERGE,
  desc: '冲突预处理',
}];

export const ExcludeMergeOptions: Array<{ label: string; value: RuleV1ExcludeMergeType }> = [
  {
    label: '该提交全部不合并',
    value: RuleV1ExcludeMergeType.EXCLUDE_CL,
  },
  {
    label: '仅匹配文件不合并',
    value: RuleV1ExcludeMergeType.EXCLUDE_FILE,
  },
];

const FileNameRuleLabelMap: Record<RuleV1FileNameRuleType, string> = {
  [RuleV1FileNameRuleType.STARTS_WITH]: '前缀为',
  [RuleV1FileNameRuleType.CONTAINS]: '包含',
  [RuleV1FileNameRuleType.ENDS_WITH]: '后缀为',
  [RuleV1FileNameRuleType.REGEX]: '正则表达式',
  [RuleV1FileNameRuleType.RULE_TYPE_INVALID]: '未知规则',
};

const FileNameRuleOptions: Array<{ label: string; value: RuleV1FileNameRuleType }> = [
  {
    label: FileNameRuleLabelMap[RuleV1FileNameRuleType.STARTS_WITH],
    value: RuleV1FileNameRuleType.STARTS_WITH,
  },
  {
    label: FileNameRuleLabelMap[RuleV1FileNameRuleType.CONTAINS],
    value: RuleV1FileNameRuleType.CONTAINS,
  },
  {
    label: FileNameRuleLabelMap[RuleV1FileNameRuleType.ENDS_WITH],
    value: RuleV1FileNameRuleType.ENDS_WITH,
  },
  {
    label: FileNameRuleLabelMap[RuleV1FileNameRuleType.REGEX],
    value: RuleV1FileNameRuleType.REGEX,
  },
];

const MergeRuleOnlineProcessFileTypeLabelMap: Record<RuleV1OnlineProcessFileType, string> = {
  [RuleV1OnlineProcessFileType.ONLINE_PROCESS_FILE_TYPE_BINARY]: 'binary文件',
  [RuleV1OnlineProcessFileType.ONLINE_PROCESS_FILE_TYPE_TEXT]: 'text文件',
  [RuleV1OnlineProcessFileType.ONLINE_PROCESS_FILE_TYPE_SYMLINK]: 'symlink文件',
  [RuleV1OnlineProcessFileType.ONLINE_PROCESS_FILE_TYPE_UNICODE]: 'unicode文件',
  [RuleV1OnlineProcessFileType.ONLINEPROCESSFILETYPEUTF8]: 'UTF-8文件',
  [RuleV1OnlineProcessFileType.ONLINEPROCESSFILETYPEUTF16]: 'UTF-16文件',
  [RuleV1OnlineProcessFileType.ONLINE_PROCESS_FILE_TYPE_INVALID]: '未知文件类型',
};

const MergeRuleOnlineProcessFileTypeOptions: Array<{ label: string; value: RuleV1OnlineProcessFileType }> = [
  {
    label: MergeRuleOnlineProcessFileTypeLabelMap[RuleV1OnlineProcessFileType.ONLINE_PROCESS_FILE_TYPE_BINARY],
    value: RuleV1OnlineProcessFileType.ONLINE_PROCESS_FILE_TYPE_BINARY,
  },
  {
    label: MergeRuleOnlineProcessFileTypeLabelMap[RuleV1OnlineProcessFileType.ONLINE_PROCESS_FILE_TYPE_TEXT],
    value: RuleV1OnlineProcessFileType.ONLINE_PROCESS_FILE_TYPE_TEXT,
  },
  {
    label: MergeRuleOnlineProcessFileTypeLabelMap[RuleV1OnlineProcessFileType.ONLINE_PROCESS_FILE_TYPE_SYMLINK],
    value: RuleV1OnlineProcessFileType.ONLINE_PROCESS_FILE_TYPE_SYMLINK,
  },
  {
    label: MergeRuleOnlineProcessFileTypeLabelMap[RuleV1OnlineProcessFileType.ONLINE_PROCESS_FILE_TYPE_UNICODE],
    value: RuleV1OnlineProcessFileType.ONLINE_PROCESS_FILE_TYPE_UNICODE,
  },
  {
    label: MergeRuleOnlineProcessFileTypeLabelMap[RuleV1OnlineProcessFileType.ONLINEPROCESSFILETYPEUTF8],
    value: RuleV1OnlineProcessFileType.ONLINEPROCESSFILETYPEUTF8,
  },
  {
    label: MergeRuleOnlineProcessFileTypeLabelMap[RuleV1OnlineProcessFileType.ONLINEPROCESSFILETYPEUTF16],
    value: RuleV1OnlineProcessFileType.ONLINEPROCESSFILETYPEUTF16,
  },
];

interface MergeBasicRuleForm {
  id?: string;
  depot?: string;
  sourceStream?: string;
  targetStream?: string;
  mergeTrigger?: RuleV1MergeTrigger;
  triggerTags?: string[];
  sourceStreamId?: number;
  targetStreamId?: number;
}

interface MergeAdvancedRuleForm {
  initialCl?: string;
  excludeUsers?: string[];
  defaultResolvers?: string[];
  defaultResolveRule?: RuleV1ResolveRule;
  ignoredTags?: string[];
  enableFeishuComment?: boolean;
  maxFiles?: number;
  useCommitTool?: boolean;
  autoRetryEnable?: boolean;
  retryIntervalMinute?: number;
  excludeRules?: (RuleV1FileNameRule & { id: string })[];
  customResolveRules?: ({
    ruleType?: RuleV1FileNameRuleType;
    pattern?: string;
    resolveRule?: RuleV1ResolveRule;
  } & { id: string })[];
  excludeUserUsers?: UserUser[];
  defaultResolverUsers?: UserUser[];
  updateSubmitter?: boolean;
  onlineProcessFileTypes?: RuleV1OnlineProcessFileType[];
  excludeFeishuItem?: (RuleV1FeishuItemValue & { id: string })[];
  additionalHandlers?: (RuleV1AdditionalHandler & { id: string })[];
}

const ResolveRuleArrowPath: Record<RuleV1ResolveRule, string> = {
  [RuleV1ResolveRule.SAFE_RESOLVE_ALLOW_MERGE]: 'M10 4.76837e-07L0 -6V6L10 4.76837e-07Z',
  [RuleV1ResolveRule.SAFE_RESOLVE_NO_MERGE]: 'M0 6L10.3926 0L0 -6V6ZM6.39258 0L2 2.53516V-2.53613L6.39258 0Z',
  [RuleV1ResolveRule.ACCEPT_SOURCE]: 'M0 -3.3743e-06L5 -5L10 -3.3743e-06L5 5L0 -3.3743e-06Z',
  [RuleV1ResolveRule.ACCEPT_TARGET]: 'M5 -5 L10 -2.56114e-09 L5 5 L0 -2.56114e-09 L5 -5 Z',
  [RuleV1ResolveRule.RESOLVE_RULE_INVALID]: '',
  [RuleV1ResolveRule.UNITY_YAML_MERGE]: '',
};

const RetryIntervalMinuteOptions = [
  {
    label: '1分钟',
    value: 1,
  },
  {
    label: '5分钟',
    value: 5,
  },
  {
    label: '10分钟',
    value: 10,
  },
  {
    label: '1小时',
    value: 60,
  },
  {
    label: '24小时',
    value: 1440,
  },
];

const MergeGraphHelpTips: Array<{ label: string; icon: () => JSX.Element }> = [
  {
    label: '开发分支',
    icon: () => <img class="h-16px w-16px" src={StreamNodeDev} />,
  },
  {
    label: '发布分支',
    icon: () => <img class="h-16px w-16px" src={StreamNodeRelease} />,
  },
  {
    label: '主干分支',
    icon: () => <img class="h-16px w-16px" src={StreamNodeMain} />,
  },
  {
    label: '启用状态',
    icon: () => <Icon class="font-size-16px c-FO-Datavis-Green1" component={<BasicFillCheck2 />} />,
  },
  {
    label: '停用状态',
    icon: () => <Icon class="font-size-16px c-FO-Functional-Error1-Default" component={<BasicFillBlock />} />,
  },
  {
    label: '冲突状态',
    icon: () => <Icon class="font-size-16px c-FO-Functional-Warning1-Default" component={<BasicFillWarning />} />,
  },
  {
    label: '合并进行中',
    icon: () => <Icon class="font-size-16px c-FO-Brand-Primary-Default" component={<BasicStrokeProcessing />} />,
  },
  {
    label: '自动合并',
    icon: () => <Icon class="c-FO-Datavis-Blue1" component={<BasicStrokeMerge />} />,
  },
  // {
  //   label: 'API合并',
  //   icon: () => <Icon class="c-FO-Datavis-Blue1" component={<BasicStrokeInterface />} />,
  // },
  {
    label: '标签合并',
    icon: () => <Icon class="c-FO-Datavis-Blue1" component={<BasicStrokeTag />} />,
  },
  {
    label: 'Safe Resolve',
    icon: () => <Icon class="font-size-16px c-transparent" component={<SafeResolveArrow />} />,
  },
  {
    label: 'Accept Merged',
    icon: () => <Icon class="font-size-16px c-transparent" component={<SafeMergedArrow />} />,
  },
  {
    label: 'Accept Source',
    icon: () => <Icon class="font-size-16px c-transparent" component={<AcceptSourceArrow />} />,
  },
  {
    label: 'Accept Target',
    icon: () => <Icon class="font-size-16px c-transparent" component={<AcceptTargetArrow />} />,
  },
];

const MergeStateLabelMap: Record<MergeV1MergeState, string> = {
  [MergeV1MergeState.MERGE_STATE_DO_NOT_MERGE]: '不合并',
  [MergeV1MergeState.MERGE_STATE_SKIPPED_MERGE]: '跳过合并',
  [MergeV1MergeState.MERGE_STATE_WAITING_MERGE]: '等待合并',
  [MergeV1MergeState.MERGE_STATE_MERGING]: '合并中',
  [MergeV1MergeState.MERGE_STATE_MERGE_FAILED]: '合并失败',
  [MergeV1MergeState.MERGE_STATE_MERGE_CONFLICT]: '冲突待解决',
  [MergeV1MergeState.MERGE_STATE_COMMIT_FAILED]: '提交失败',
  [MergeV1MergeState.MERGE_STATE_MERGED]: '已合并',
  [MergeV1MergeState.MERGE_STATE_INVALID]: '未知状态',
};

const MergeStateOptions: Array<{ label: string; value: MergeV1MergeState }> = [
  {
    label: MergeStateLabelMap[MergeV1MergeState.MERGE_STATE_DO_NOT_MERGE],
    value: MergeV1MergeState.MERGE_STATE_DO_NOT_MERGE,
  },
  {
    label: MergeStateLabelMap[MergeV1MergeState.MERGE_STATE_SKIPPED_MERGE],
    value: MergeV1MergeState.MERGE_STATE_SKIPPED_MERGE,
  },
  {
    label: MergeStateLabelMap[MergeV1MergeState.MERGE_STATE_WAITING_MERGE],
    value: MergeV1MergeState.MERGE_STATE_WAITING_MERGE,
  },
  {
    label: MergeStateLabelMap[MergeV1MergeState.MERGE_STATE_MERGING],
    value: MergeV1MergeState.MERGE_STATE_MERGING,
  },
  {
    label: MergeStateLabelMap[MergeV1MergeState.MERGE_STATE_MERGE_FAILED],
    value: MergeV1MergeState.MERGE_STATE_MERGE_FAILED,
  },
  {
    label: MergeStateLabelMap[MergeV1MergeState.MERGE_STATE_MERGE_CONFLICT],
    value: MergeV1MergeState.MERGE_STATE_MERGE_CONFLICT,
  },
  {
    label: MergeStateLabelMap[MergeV1MergeState.MERGE_STATE_COMMIT_FAILED],
    value: MergeV1MergeState.MERGE_STATE_COMMIT_FAILED,
  },
  {
    label: MergeStateLabelMap[MergeV1MergeState.MERGE_STATE_MERGED],
    value: MergeV1MergeState.MERGE_STATE_MERGED,
  },
];

const renderMergeStateIcon: Record<MergeV1MergeState, () => JSX.Element | null> = {
  [MergeV1MergeState.MERGE_STATE_DO_NOT_MERGE]: () => <Icon class="font-size-16px c-FO-Content-Icon2" component={<Close />} />,
  [MergeV1MergeState.MERGE_STATE_SKIPPED_MERGE]: () => <Icon class="font-size-16px c-FO-Content-Icon2" component={<BasicStrokeSkip />} />,
  [MergeV1MergeState.MERGE_STATE_WAITING_MERGE]: () => <Icon class="font-size-16px c-FO-Functional-Info1-Default" component={<BasicStrokeClock />} />,
  [MergeV1MergeState.MERGE_STATE_MERGING]: () => <Icon class="font-size-16px c-FO-Brand-Primary-Default" component={<BasicStrokeProcessing />} />,
  [MergeV1MergeState.MERGE_STATE_MERGE_FAILED]: () => <Icon class="font-size-16px c-FO-Functional-Error1-Default" component={<Subtract />} />,
  [MergeV1MergeState.MERGE_STATE_MERGE_CONFLICT]: () => <Icon class="font-size-16px c-FO-Functional-Warning1-Default" component={<BasicFillWarning />} />,
  [MergeV1MergeState.MERGE_STATE_COMMIT_FAILED]: () => <Icon class="font-size-16px c-FO-Functional-Error1-Default" component={<Subtract />} />,
  [MergeV1MergeState.MERGE_STATE_MERGED]: () => <Icon class="font-size-16px c-FO-Datavis-Green1" component={<BasicFillCheck2 />} />,
  [MergeV1MergeState.MERGE_STATE_INVALID]: () => null,
};

const MergeV1FailReasonLabelMap: Record<MergeV1FailReason, string> = {
  [MergeV1FailReason.FAIL_REASON_CHECKOUT_FAILED]: '独占签出',
  [MergeV1FailReason.FAIL_REASON_COMMIT_FAILED]: '提交失败',
  [MergeV1FailReason.FAIL_REASON_CONFLICT]: '文件冲突',
  [MergeV1FailReason.FAIL_REASON_INVALID]: '未知错误',
  [MergeV1FailReason.FAIL_REASON_OTHER]: '其他错误',
};

const MergeV1FailReasonOptions: Array<{ label: string; value: MergeV1FailReason }> = [
  {
    label: MergeV1FailReasonLabelMap[MergeV1FailReason.FAIL_REASON_CHECKOUT_FAILED],
    value: MergeV1FailReason.FAIL_REASON_CHECKOUT_FAILED,
  },
  {
    label: MergeV1FailReasonLabelMap[MergeV1FailReason.FAIL_REASON_COMMIT_FAILED],
    value: MergeV1FailReason.FAIL_REASON_COMMIT_FAILED,
  },
  {
    label: MergeV1FailReasonLabelMap[MergeV1FailReason.FAIL_REASON_CONFLICT],
    value: MergeV1FailReason.FAIL_REASON_CONFLICT,
  },
  {
    label: MergeV1FailReasonLabelMap[MergeV1FailReason.FAIL_REASON_OTHER],
    value: MergeV1FailReason.FAIL_REASON_OTHER,
  },
  {
    label: MergeV1FailReasonLabelMap[MergeV1FailReason.FAIL_REASON_INVALID],
    value: MergeV1FailReason.FAIL_REASON_INVALID,
  },
];

const renderMergeV1FailReasonIcon: Record<MergeV1FailReason, () => JSX.Element | null> = {
  [MergeV1FailReason.FAIL_REASON_CHECKOUT_FAILED]: () => <Icon class="font-size-16px c-FO-Functional-Warning1-Default" component={<BasicFillWarning />} />,
  [MergeV1FailReason.FAIL_REASON_COMMIT_FAILED]: () => <Icon class="font-size-16px c-FO-Functional-Error1-Default" component={<Subtract />} />,
  [MergeV1FailReason.FAIL_REASON_CONFLICT]: () => <Icon class="font-size-16px c-FO-Functional-Warning1-Default" component={<BasicFillWarning />} />,
  [MergeV1FailReason.FAIL_REASON_INVALID]: () => null,
  [MergeV1FailReason.FAIL_REASON_OTHER]: () => <Icon class="font-size-16px c-FO-Functional-Warning1-Default" component={<BasicFillWarning />} />,
};

const MergeV1ResolveRuleLabelMap: Record<MergeV1ResolveRule, string> = {
  [MergeV1ResolveRule.RESOLVE_RULE_INVALID]: '待处理',
  [MergeV1ResolveRule.SAFE_RESOLVE_NO_MERGE]: 'Safe Resolve (No Merge)',
  [MergeV1ResolveRule.SAFE_RESOLVE_ALLOW_MERGE]: 'Safe Resolve (Allow Merge)',
  [MergeV1ResolveRule.ACCEPT_SOURCE]: '已接受左边修改',
  [MergeV1ResolveRule.ACCEPT_TARGET]: '已保留右边内容',
};

const MergeV1ResolveRuleOperateOptions: Partial<Record<MergeV1ResolveRule, string>> = {
  [MergeV1ResolveRule.RESOLVE_RULE_INVALID]: '改为待处理',
  [MergeV1ResolveRule.ACCEPT_SOURCE]: '接受左边修改',
  [MergeV1ResolveRule.ACCEPT_TARGET]: '保留右边内容',
};

const renderMergeV1ResolveRuleIcon: Record<MergeV1ResolveRule, () => JSX.Element | null> = {
  [MergeV1ResolveRule.RESOLVE_RULE_INVALID]: () => <Icon class="font-size-16px c-FO-Datavis-Blue1" component={<BasicStrokeClock />} />,
  [MergeV1ResolveRule.SAFE_RESOLVE_NO_MERGE]: () => null,
  [MergeV1ResolveRule.SAFE_RESOLVE_ALLOW_MERGE]: () => null,
  [MergeV1ResolveRule.ACCEPT_SOURCE]: () => <Icon class="font-size-16px c-FO-Datavis-Green1" component={<BasicFillCheck2 />} />,
  [MergeV1ResolveRule.ACCEPT_TARGET]: () => <Icon class="font-size-16px c-FO-Datavis-Green1" component={<BasicFillCheck2 />} />,
};

const renderBranchTypeIcon: Record<RuleV1StreamType, () => JSX.Element | null> = {
  [RuleV1StreamType.DEVELOPMENT]: () => <img class="h-16px w-16px" src={StreamNodeDev} />,
  [RuleV1StreamType.RELEASE]: () => <img class="h-16px w-16px" src={StreamNodeRelease} />,
  [RuleV1StreamType.MAINLINE]: () => <img class="h-16px w-16px" src={StreamNodeMain} />,
  [RuleV1StreamType.STREAM_TYPE_INVALID]: () => null,
};

const MergeV1OperationTypeLabelMap: Record<MergeV1OperationType, string> = {
  [MergeV1OperationType.OPERATION_TYPE_ASSIGN_HANDLER]: '分配',
  [MergeV1OperationType.OPERATION_TYPE_RETRY]: '合并重试',
  [MergeV1OperationType.OPERATION_TYPE_LOCAL_PROCESS]: '本地解决冲突',
  [MergeV1OperationType.OPERATION_TYPE_SKIP]: '跳过',
  [MergeV1OperationType.OPERATION_TYPE_SAVE_RESOLVE_PROGRESS]: '保存进度',
  [MergeV1OperationType.OPERATION_TYPE_ONLINE_PROCESS]: '在线解决冲突',
  [MergeV1OperationType.OPERATION_TYPE_INVALID]: '未知操作',
};

const renderOperationTypeIcon: Record<MergeV1OperationType, () => JSX.Element | null> = {
  [MergeV1OperationType.OPERATION_TYPE_ASSIGN_HANDLER]: () => <Icon class="font-size-16px c-FO-Content-Icon2" component={<BasicStrokeAssign />} />,
  [MergeV1OperationType.OPERATION_TYPE_RETRY]: () => <Icon class="font-size-16px c-FO-Content-Icon2" component={<BasicStrokeReset />} />,
  [MergeV1OperationType.OPERATION_TYPE_LOCAL_PROCESS]: () => <Icon class="font-size-16px c-FO-Content-Icon2" component={<BasicStrokeConflict />} />,
  [MergeV1OperationType.OPERATION_TYPE_SKIP]: () => <Icon class="font-size-16px c-FO-Content-Icon2" component={<BasicStrokeSkip />} />,
  [MergeV1OperationType.OPERATION_TYPE_SAVE_RESOLVE_PROGRESS]: () => <Icon class="font-size-16px c-FO-Content-Icon2" component={<BasicStrokeLoader />} />,
  [MergeV1OperationType.OPERATION_TYPE_ONLINE_PROCESS]: () => <Icon class="font-size-16px c-FO-Content-Icon2" component={<BasicStrokeConflict />} />,
  [MergeV1OperationType.OPERATION_TYPE_INVALID]: () => null,
};

const NotifyScheduleTypeMap = {
  [ApiNotifyV1NotifyScheduleType.DAILY]: '每日',
  [ApiNotifyV1NotifyScheduleType.WEEKLY]: '每周',
  [ApiNotifyV1NotifyScheduleType.NOTIFY_TYPE_INVALID]: '未知',
};

const NotifyScheduleTypeOptions = [
  {
    label: NotifyScheduleTypeMap[ApiNotifyV1NotifyScheduleType.DAILY],
    value: ApiNotifyV1NotifyScheduleType.DAILY,
  },
  {
    label: NotifyScheduleTypeMap[ApiNotifyV1NotifyScheduleType.WEEKLY],
    value: ApiNotifyV1NotifyScheduleType.WEEKLY,
  },
];

const RuleV1FeishuItemOperationLabelMap: Record<RuleV1FeishuItemOperation, string> = {
  [RuleV1FeishuItemOperation.FEISHU_ITEM_OPERATION_CONTAIN]: '包含',
  [RuleV1FeishuItemOperation.FEISHU_ITEM_OPERATION_NOT_CONTAIN]: '不包含',
  [RuleV1FeishuItemOperation.FEISHU_ITEM_OPERATION_INVALID]: '未知操作',
};

const RuleV1FeishuItemOperationOptions: Array<{ label: string; value: RuleV1FeishuItemOperation }> = [
  {
    label: RuleV1FeishuItemOperationLabelMap[RuleV1FeishuItemOperation.FEISHU_ITEM_OPERATION_CONTAIN],
    value: RuleV1FeishuItemOperation.FEISHU_ITEM_OPERATION_CONTAIN,
  },
  {
    label: RuleV1FeishuItemOperationLabelMap[RuleV1FeishuItemOperation.FEISHU_ITEM_OPERATION_NOT_CONTAIN],
    value: RuleV1FeishuItemOperation.FEISHU_ITEM_OPERATION_NOT_CONTAIN,
  },
];

enum FeishuWorkItem {
  story = 'story',
  issue = 'issue',
}

const FeishuWorkItemLabelMap: Record<FeishuWorkItem, string> = {
  [FeishuWorkItem.story]: '需求',
  [FeishuWorkItem.issue]: '缺陷',
};

const FeishuWorkItemOptions: Array<{ label: string; value: FeishuWorkItem }> = [
  {
    label: FeishuWorkItemLabelMap[FeishuWorkItem.story],
    value: FeishuWorkItem.story,
  },
  {
    label: FeishuWorkItemLabelMap[FeishuWorkItem.issue],
    value: FeishuWorkItem.issue,
  },
];

const AdditionItemFieldLabelMap: Record<RuleV1AdditionalHandlerField, string> = {
  [RuleV1AdditionalHandlerField.FIELD_SUBMITTER]: '提交人',
  [RuleV1AdditionalHandlerField.FIELD_INVALID]: '未知字段',
};

const AdditionItemFieldOptions: Array<{ label: string; value: RuleV1AdditionalHandlerField }> = [
  {
    label: AdditionItemFieldLabelMap[RuleV1AdditionalHandlerField.FIELD_SUBMITTER],
    value: RuleV1AdditionalHandlerField.FIELD_SUBMITTER,
  },
];

const AdditionItemOperationLabelMap: Record<RuleV1HandlerOperation, string> = {
  [RuleV1HandlerOperation.HANDLER_OPERATION_EQUAL]: '等于',
  [RuleV1HandlerOperation.HANDLER_OPERATION_INVALID]: '未知操作',
};

const AdditionItemOperationOptions: Array<{ label: string; value: RuleV1HandlerOperation }> = [
  {
    label: AdditionItemOperationLabelMap[RuleV1HandlerOperation.HANDLER_OPERATION_EQUAL],
    value: RuleV1HandlerOperation.HANDLER_OPERATION_EQUAL,
  },
];

export {
  AdditionItemFieldLabelMap,
  AdditionItemFieldOptions,
  AdditionItemOperationLabelMap,
  AdditionItemOperationOptions,
  CustomMergeRuleOptions,
  FeishuWorkItem,
  FeishuWorkItemLabelMap,
  FeishuWorkItemOptions,
  FileNameRuleOptions,
  MergeGraphHelpTips,
  MergeRuleFormPanelKey,
  MergeRuleLabelMap,
  MergeRuleOnlineProcessFileTypeLabelMap,
  MergeRuleOnlineProcessFileTypeOptions,
  MergeRuleOptions,
  MergeStateLabelMap,
  MergeStateOptions,
  MergeV1FailReasonLabelMap,
  MergeV1FailReasonOptions,
  MergeV1MergeState,
  MergeV1OperationTypeLabelMap,
  MergeV1ResolveRuleLabelMap,
  MergeV1ResolveRuleOperateOptions,
  NotifyScheduleTypeOptions,
  renderBranchTypeIcon,
  renderMergeStateIcon,
  renderMergeV1FailReasonIcon,
  renderMergeV1ResolveRuleIcon,
  renderOperationTypeIcon,
  ResolveRuleArrowPath,
  RetryIntervalMinuteOptions,
  RuleV1FeishuItemOperationLabelMap,
  RuleV1FeishuItemOperationOptions,
  TriggerTypeLabelMap,
  TriggerTypeOptions,
};
export type { MergeAdvancedRuleForm, MergeBasicRuleForm };
