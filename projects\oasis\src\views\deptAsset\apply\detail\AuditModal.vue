<template>
  <BasicModal
    :wrapClassName="prefixCls"
    :footer="null"
    @register="registerModal"
    @ok="handleSubmit"
  >
    <template #title>
      <template v-if="isDirectReturn">
        确认设备已归还吗?
      </template>
      <template v-else>
        {{ `确认${isApprove ? '同意' : '拒绝'}设备${isReturning ? '归还' : '借用'}申请吗?` }}
      </template>
    </template>
    <div class="p-4">
      <ATextarea
        v-model:value="remark"
        :placeholder="isDirectReturn ? '请输入备注' : `请输入${isApprove ? '同意' : '拒绝'}理由`"
        :maxLength="100"
        :rows="4"
        type="text"
        inputMode="text"
      />
    </div>
    <div class="flex justify-end gap-2 p-4">
      <AButton :loading="loading" @click="closeModal">
        取消
      </AButton>
      <AButton type="primary" :loading="loading" @click="handleSubmit">
        确认
      </AButton>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import type { ModalMethods } from '/@/components/Modal';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { Textarea as ATextarea } from 'ant-design-vue';
import { devicesReturnBatchDirect, directReturnDevice, editApplyLog, editReturnLog } from '/@/api/page/deptAsset';
import { DeviceApplyStateEnum } from '/@/api/page/model/deptAssetModel';
import { useDesign } from '/@/hooks/web/useDesign';

const emit = defineEmits<{
  success: [];
  register: [methods: ModalMethods, uuid: number];
}>();
const { prefixCls } = useDesign('dept-asset-apply-audit-modal');

const deviceID = ref<number>();
const deviceIds = ref<number[]>([]);
const isBatch = ref<boolean>(false);
const applyID = ref<number>();
const returnID = ref<number>();
const isReturning = ref<boolean>(false);
const isDirectReturn = ref<boolean>(false);
const isApprove = ref<boolean>(false);
const remark = ref<string>('');
const loading = ref<boolean>(false);

const [registerModal, { closeModal }] = useModalInner(async (data) => {
  loading.value = true;
  deviceID.value = data?.deviceID;
  deviceIds.value = data?.deviceIds;
  isBatch.value = data?.isBatch;
  applyID.value = data?.applyID;
  returnID.value = data?.returnID;
  isReturning.value = !!data?.isReturning;
  isApprove.value = !!data?.isApprove;
  isDirectReturn.value = !!data?.isDirectReturn;
  remark.value = '';
  loading.value = false;
});

async function handleSubmit() {
  try {
    loading.value = true;
    const editApi = isReturning.value ? editReturnLog : editApplyLog;
    const editID = isReturning.value ? returnID.value : applyID.value;
    if (isBatch.value) {
      const res = await devicesReturnBatchDirect(
        {
          deviceIds: deviceIds.value,
          remark: remark.value,
        },
      );
      if (res?.code === 7) {
        return;
      }
    } else {
      if (!deviceID.value) {
        return;
      }
      if (isDirectReturn.value) {
        const res = await directReturnDevice(
          {
            deviceID: deviceID.value,
            remark: remark.value,
            state: DeviceApplyStateEnum.APPROVED,
          },
        );
        if (res?.code === 7) {
          return;
        }
      } else {
        if (!editID) {
          return;
        }
        const res = await editApi(
          {
            deviceID: deviceID.value,
            state: isApprove.value ? DeviceApplyStateEnum.APPROVED : DeviceApplyStateEnum.REJECTED,
            remark: remark.value,
          },
          editID,
        );
        if (res?.code === 7) {
          return;
        }
      }
    }
    emit('success');
    closeModal();
  } finally {
    loading.value = false;
  }
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-dept-asset-apply-audit-modal';
@prefix-cls-close: ~'hypergryph-basic-modal-close';

.@{prefix-cls} {
  & .@{prefix-cls-close} {
    top: 4px !important;
  }
  & .ant-modal-header {
    border-bottom: 1px solid @FO-Container-Stroke1;
    padding: 8px 16px;
    max-height: 40px;
  }
}
</style>
