import { type UserUser, RuleV1StreamType } from '@hg-tech/api-schema-merge';
import { type PropType, computed, defineComponent } from 'vue';
import Icon from '@ant-design/icons-vue';
import BasicStrokeChevronRight from '../../../assets/svg/BasicStrokeChevronRight.svg?component';
import { UserTag } from '../../../components/UserTag';
import { Popover } from 'ant-design-vue';
import { useMergeTask } from '../use-merge-task';
import { renderBranchTypeIcon } from '../../../models/config.model';

const DrawerHeader = defineComponent({
  props: {
    cl: {
      type: String as PropType<string>,
      default: '',
    },
    user: {
      type: Object as PropType<UserUser>,
      default: () => ({}),
    },
  },
  setup(props, { slots }) {
    const { currentBranchMap, ruleList, currentRuleId } = useMergeTask();

    const currentRule = computed(() => {
      return ruleList.value.find((rule) => rule.id === currentRuleId.value);
    });

    const renderRuleItem = (streamId: number) => {
      const branch = currentBranchMap.value.get(streamId!);
      return (
        <Popover placement="top" z-index={2000}>
          {{
            default: () => (
              <div class="branch-item truncate">
                <div class="FO-Font-B14 mb-4px flex items-center gap-4px c-FO-Content-Text1">
                  {renderBranchTypeIcon[branch?.streamType ?? RuleV1StreamType.DEVELOPMENT]()}
                  {branch?.name || '--'}
                </div>
                <div class="truncate c-FO-Content-Text3">{branch?.path || '--'}</div>
              </div>
            ),
            title: () => (
              <div class="branch-info">
                <div class="branch-item-status-text FO-Font-B14 flex items-center gap-4px">
                  {renderBranchTypeIcon[branch?.streamType ?? RuleV1StreamType.DEVELOPMENT]()}
                  {branch?.name || '--'}
                </div>
                <div class="branch-item-status-text FO-Font-R14">
                  {branch?.path}
                </div>
              </div>
            ),
          }}
        </Popover>
      );
    };

    const renderRule = () => {
      return (
        <div class="flex items-center justify-between gap-8px">
          <div class="flex-grow-1 truncate rd-6px bg-FO-Container-Fill2 p-12px">
            {renderRuleItem(currentRule.value?.sourceStreamId ?? 0)}
          </div>
          <Icon class="flex-shrink-0 font-size-18px" component={<BasicStrokeChevronRight />} />
          <div class="flex-grow-1 truncate rd-6px bg-FO-Container-Fill2 p-12px">
            {renderRuleItem(currentRule.value?.targetStreamId ?? 0)}
          </div>
        </div>
      );
    };

    return () => (
      <div class="drawer-content-header mb-24px">
        <div class="mb-12px">
          {renderRule()}
        </div>

        <div class="resolver-content-info flex items-center justify-between">
          <div class="flex items-center gap-24px">
            <div class="flex items-center gap-4px">
              冲突CL：
              <span class="FO-Font-B16">{props?.cl}</span>
            </div>
            <div class="flex items-center gap-4px">
              提交人：
              <span>
                <UserTag
                  avatarSize={24}
                  user={{
                    openId: props.user?.feishuOpenId || '',
                    name: props.user?.name || props.user?.hgAccount || '--',
                    avatar: props.user?.avatar || '',
                    nickname: props.user?.nickname || '',
                  }}
                />
              </span>
            </div>
          </div>
          {slots.default?.()}
        </div>
      </div>
    );
  },
});

export {
  DrawerHeader,
};
