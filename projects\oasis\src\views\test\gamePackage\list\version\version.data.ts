import type { FormSchema } from '/@/components/Table';

export const formSchema: FormSchema[] = [
  {
    field: 'baseSetting',
    label: '基本设置',
    component: 'BorderBox',
    children: [
      {
        field: 'version',
        label: '版本',
        required: true,
        component: 'Input',
        componentProps: {
          placeholder: '请输入版本',
        },
      },
      {
        field: 'platform',
        label: '平台',
        helpMessage: '会根据上传的文件格式自动判断平台',
        required: true,
        component: 'RadioGroup',
        defaultValue: 1,
      },
      {
        field: 'reserve',
        label: '是否长期保留',
        defaultValue: false,
        component: 'Switch',
        componentProps: {
          checkedChildren: '是',
          unCheckedChildren: '否',
        },
      },
      {
        label: '包文件',
        field: 'pkgFile',
        component: 'Upload',
        valueField: 'singleValue',
        rules: [{ required: true, message: '请上传打包文件' }],
        componentProps: {
          uploadType: 'appstore',
          valueFormat: 'string',
          maxNumber: 1,
          multiple: false,
          noAlert: true,
        },
      },
      {
        label: '包大小',
        show: false,
        field: 'sizeKB',
        component: 'InputNumber',
      },
      {
        label: '关联资源',
        field: 'attachments',
        component: 'Input',
        slot: 'attachments',
      },
      {
        field: 'releaseNote',
        label: '备注',
        component: 'InputTextArea',
        componentProps: {
          placeholder: '请输入备注',
        },
      },
    ],
  },
  {
    field: 'CloudGameSetting',
    label: '云游戏部署',
    component: 'BorderBox',
    children: [{
      field: 'deploy',
      label: '部署云游戏',
      component: 'Switch',
      componentProps: {
        checkedChildren: '是',
        unCheckedChildren: '否',
      },
      itemProps: {
        extra: '若开启开关，上传包体后会执行云游戏部署',
      },
    }],
  },
];
