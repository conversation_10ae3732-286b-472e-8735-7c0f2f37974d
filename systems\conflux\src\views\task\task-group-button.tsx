import { type PropType, computed, defineComponent, ref, watch } from 'vue';
import { Button, message, Popconfirm, Switch, Tooltip } from 'ant-design-vue';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { mergeApi } from '../../api';
import { traceCustomEvent } from '../../services/track';
import { TrackEventName } from '../../constants/event';

import GroupUpdate from '../../assets/svg/GroupUpdate.svg?component';
import GroupAdd from '../../assets/svg/GroupAdd.svg?component';

export const TaskGroupButton = defineComponent({
  props: {
    projectId: Number as PropType<number | undefined>,
    ruleId: String as PropType<string | undefined>,
    sourcePath: String as PropType<string | undefined>,
    targetPath: String as PropType<string | undefined>,
  },
  setup(props) {
    const { data: chatInfo, execute: fetchChatInfo } = useLatestPromise(mergeApi.v1.notifyGetInfo);
    const { execute: createChat, loading: creatingChatLoading } = useLatestPromise(mergeApi.v1.notifyJoinChat);
    const { execute: updateChatSetting, loading: chatSettingLoading } = useLatestPromise(mergeApi.v1.notifyUpdateInfo);
    const chatId = computed(() => chatInfo.value?.data?.data?.chatId);
    const isChatCreateByError = ref<boolean>(false);

    async function onGetChatInfo() {
      if (!props.projectId || !props.ruleId) {
        return;
      }
      await fetchChatInfo({ id: props.projectId, ruleId: props.ruleId }, {});
      isChatCreateByError.value = Boolean(chatInfo.value?.data?.data?.mergeFailedCreateChat);
    }

    watch([() => props.projectId, () => props.ruleId], ([pId, rId]) => {
      if (!pId || !rId) {
        return;
      }
      onGetChatInfo();
    }, { immediate: true });

    async function handleCreateGroup() {
      if (!props.projectId || !props.ruleId) {
        return;
      }
      traceCustomEvent(chatId.value ? TrackEventName.conflux_feishu_group_update : TrackEventName.conflux_feishu_group_create, {
        conflux_rule_id: props.ruleId,
        conflux_source_branch_path: props.sourcePath,
        conflux_target_branch_path: props.targetPath,
      });
      const res = await createChat({ id: props.projectId }, { ruleId: props.ruleId });
      if (res?.data?.data?.chatId) {
        window.open(`https://applink.feishu.cn/client/chat/open?openChatId=${res?.data?.data?.chatId}`, '_blank', 'noopener,noreferrer');
        message.success('请求已发送，请至飞书查看通知');
        await onGetChatInfo();
      }
    }

    async function handleSwitchChange(checked: boolean) {
      if (!props.projectId || !props.ruleId || chatSettingLoading.value) {
        return;
      }
      const res = await updateChatSetting({ id: props.projectId, ruleId: props.ruleId }, { mergeFailedCreateChat: checked });
      if (res?.data?.data) {
        await onGetChatInfo();
        message.success(`自动执行已${checked ? '开启' : '关闭'}`);
      }
    }

    return () => {
      return (
        <div class="flex items-center gap-8px">
          <div class="flex items-center gap-8px">
            <span class="FO-Font-B14">自动执行</span>
            <Tooltip title={isChatCreateByError.value ? '关闭后需要手动建群和更新' : '打开后在合并失败时自动建群或更新'} trigger="hover">
              <Switch
                checked={isChatCreateByError.value}
                checkedValue={true}
                loading={chatSettingLoading.value}
                onChange={(check) => handleSwitchChange(check as boolean)}
                unCheckedValue={false}
              />
            </Tooltip>
          </div>
          <Popconfirm cancelText="取消" okText="确认" onConfirm={handleCreateGroup} placement="bottomLeft">
            {{
              default: () => (
                <Tooltip title={chatId.value ? '点击更新群聊成员' : '将所有处理人拉入群聊'} trigger="hover">
                  <Button class="btn-fill-secondary" loading={creatingChatLoading.value}>
                    {{
                      icon: () => (chatId.value ? <GroupUpdate class="font-size-16px" /> : <GroupAdd class="font-size-16px" />),
                      default: () => (chatId.value ? '更新成员' : '一键拉群'),
                    }}
                  </Button>
                </Tooltip>
              ),
              title: () => (
                <div class="w-200px c-FO-Content-Text2">
                  确定要{chatId.value ? '更新' : '创建'}群聊吗？
                </div>
              ),
            }}
          </Popconfirm>

        </div>
      );
    };
  },
});
