import Icon from '@ant-design/icons-vue';
import { Button, Drawer, Form, Input, message, Select } from 'ant-design-vue';
import { type PropType, computed, defineComponent, ref } from 'vue';
import type { MergeV1MergeRecord, MergeV1MergeServiceSkipMergeBody } from '@hg-tech/api-schema-merge';
import type { Rule } from 'ant-design-vue/es/form';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { DrawerHeader } from '../components/drawer-header';
import { useMergeTask } from '../use-merge-task';
import { mergeApi } from '../../../api';
import { traceCustomEvent } from '../../../services/track';
import { TrackEventName } from '../../../constants/event';

import Close from '../../../assets/svg/Close.svg?component';

const SkipResolver = defineComponent({
  props: {
    visible: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    data: {
      default: () => ({}),
      type: Object as PropType<MergeV1MergeRecord>,
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const { skipReasons, currentProjectId, currentRuleId, ruleList, fetchTaskList, currentBranchMap } = useMergeTask();
    const { execute: skipMerge } = useLatestPromise(mergeApi.v1.mergeServiceSkipMerge);

    const formRef = ref();
    const form = ref({
      reason: undefined,
      other: '',
    });
    const rules: Record<string, Rule[]> = {
      reason: [
        {
          required: true,
          message: '请填写跳过理由',
        },
      ],
      other: [
        {
          required: true,
          message: '请输入其他理由',
        },
      ],
    };

    const open = computed({
      get: () => props.visible,
      set: (value) => {
        emit('update:visible', value);
      },
    });
    const currentRule = computed(() => ruleList.value.find((rule) => rule.id === currentRuleId.value));

    const onSkipMerge = async (params: MergeV1MergeServiceSkipMergeBody) => {
      if (!currentProjectId.value || !params.recordId || !params.reason) {
        return;
      }
      await skipMerge({ id: currentProjectId.value }, params);
      fetchTaskList();
    };

    const onClose = () => {
      open.value = false;
    };
    const onSubmit = async () => {
      await formRef.value.validate();
      traceCustomEvent(TrackEventName.conflux_failed_merge_skip, {
        conflux_rule_id: currentRuleId.value,
        conflux_source_branch_path: currentRule.value?.sourceStreamId ? currentBranchMap.value.get(currentRule.value?.sourceStreamId)?.path : undefined,
        conflux_target_branch_path: currentRule.value?.targetStreamId ? currentBranchMap.value.get(currentRule.value?.targetStreamId)?.path : undefined,
      });
      await onSkipMerge({
        recordId: props.data.id,
        reason: form.value.reason === 'other' ? form.value.other : form.value.reason,
      });
      message.success('跳过成功');
      open.value = false;
    };

    return () => (
      <Drawer
        bodyStyle={{ padding: '24px', overflow: 'hidden' }}
        closable={false}
        destroyOnClose={true}
        mask={true}
        maskClosable={false}
        onClose={onClose}
        placement="right"
        title="跳过冲突"
        v-model:open={open.value}
        width={924}
      >
        {{
          extra: () => (
            <Button
              class="flex items-center justify-center"
              icon={(
                <Icon class="font-size-18px" component={<Close />} />
              )}
              onClick={onClose}
              type="text"
            />
          ),
          default: () => (
            <div class="resolver-content h-full flex flex-col">
              <DrawerHeader cl={props.data?.cl} user={props.data?.submitter} />
              <Form layout="vertical" model={form.value} ref={formRef} rules={rules}>
                <Form.Item label={<span class="FO-Font-B14">请选择跳过理由</span>} name="reason">
                  <Select allowClear placeholder="请选择跳过理由" v-model:value={form.value.reason}>
                    {skipReasons.value.map((item) => (
                      <Select.Option key={item} value={item}>
                        {item}
                      </Select.Option>
                    ))}
                    <Select.Option value="other">其他</Select.Option>
                  </Select>
                </Form.Item>
                {form.value.reason === 'other' && (
                  <Form.Item name="other">
                    <Input.TextArea allowClear autoSize={false} maxlength={1000} placeholder="请输入其他信息" rows={5} v-model:value={form.value.other} />
                  </Form.Item>
                )}
              </Form>
            </div>
          ),
          footer: () => (
            <div class="flex items-center justify-end gap-12px">
              <Button class="btn-fill-default" onClick={onClose} type="text">取消</Button>
              <Button class="btn-fill-primary" disabled={!form.value.reason} onClick={onSubmit} type="primary">提交</Button>
            </div>
          ),
        }}
      </Drawer>
    );
  },
});

export {
  SkipResolver,
};
