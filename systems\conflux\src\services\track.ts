import type { RouteLocationNormalized } from 'vue-router';
import { findItemInMenuByPath, ModulesMenuConfig, useForgeonTrackCtx, useMicroAppInject } from '@hg-tech/oasis-common';
import type { TrackEventName, TrackEventParams } from '../constants/event';
import { useAppThemeStore } from '../store/modules/appTheme';
import { store } from '../store/pinia';

interface CommonParams {
  /**
   * 当前路由路径
   */
  route_path: string;
  /**
   * 前路由路径
   */
  prv_route_path: string;
  /**
   * 前路由所属业务层级
   */
  business_level_prv: string;
  /**
   * 所属业务层级
   */
  business_level_cur: string;
  /**
   * 主题
   */
  theme: string;
  /**
   * 当前route_name
   */
  route_name?: string;
  /**
   * 前route_name
   */
  prv_route_name?: string;
}

let commonParams: CommonParams = {
  route_path: '',
  prv_route_path: '',
  business_level_cur: '',
  business_level_prv: '',
  theme: '',
  prv_route_name: '',
  route_name: '',
};

function setTrackCommonParams(params: Partial<CommonParams>) {
  commonParams = { ...commonParams, ...params };
}

function getTrackCommonParams() {
  return { ...commonParams };
}

function beforeLog() {
  const { theme } = useAppThemeStore(store);
  const { route_path, prv_route_path } = getTrackCommonParams();
  const { path: prvPath } = findItemInMenuByPath(ModulesMenuConfig, prv_route_path);
  const { path } = findItemInMenuByPath(ModulesMenuConfig, route_path);

  setTrackCommonParams({
    business_level_cur: path.join('/') || '',
    business_level_prv: prvPath.join('/'),
    route_name: path[path.length - 1],
    prv_route_name: prvPath[prvPath.length - 1] || '',
    theme,
  });
}

let webEventTracker: {
  sendPV: (params: {
    data: { route_name: string; route_path: string; prv_route_name?: string; prv_route_path?: string };
  }) => void;
  sendEvent: (eventName: TrackEventName, params: Record<string, any>) => void;
} | undefined;

// 处理微应用埋点
// 微应用埋点使用主应用的埋点能力
if (window.__MICRO_APP_ENVIRONMENT__) {
  const { data: track } = useMicroAppInject(useForgeonTrackCtx);
  webEventTracker = track.value;
}

function sendLog(eventName: TrackEventName, eventData: Record<string, any>) {
  try {
    beforeLog();
    const dataWithCommonParams = { ...eventData };
    webEventTracker?.sendEvent(eventName, dataWithCommonParams);
  } catch (e) {
    console.error(`[sendLog] Failed to send ${eventName} event:`, e);
  }
}

/**
 * 记录路由跳转
 */
export function traceRouteChange(cur: RouteLocationNormalized, from?: RouteLocationNormalized) {
  try {
    beforeLog();
    setTrackCommonParams({
      route_path: cur.path,
      prv_route_path: from?.path || '',
    });
    webEventTracker?.sendPV({
      data: {
        route_name: cur.name as string,
        route_path: cur.path,
        prv_route_name: from?.name as string,
        prv_route_path: from?.name ? from?.path : undefined,
      },
    });
  } catch (e) {
    console.error('[traceRouteChange]路由埋点失败：', e);
  }
}

/**
 * 记录自定义事件
 */
export function traceCustomEvent<T extends TrackEventName>(eventName: T, eventData?: TrackEventParams<T>) {
  try {
    sendLog(eventName, {
      ...eventData,
    });
  } catch (e) {
    console.error('[traceCustomEvent]自定义事件埋点失败：', e);
  }
}
