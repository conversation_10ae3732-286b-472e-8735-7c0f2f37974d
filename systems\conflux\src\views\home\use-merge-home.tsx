import { createSharedComposable } from '@vueuse/core';
import { useForgeonConfigStore } from '../../store/modules/forgeonConfig';
import { store } from '../../store/pinia';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { mergeApi } from '../../api';
import { type RuleV1Depot, type RuleV1Field, type RuleV1Rule, type RuleV1Stream, RuleV1MergeTrigger } from '@hg-tech/api-schema-merge';
import { computed, ref } from 'vue';
import { FeishuWorkItem } from '../../models/config.model';
import { traceCustomEvent } from '../../services/track';
import { TrackEventName } from '../../constants/event';

const useMergeHome = createSharedComposable(() => {
  const forgeonConfig = useForgeonConfigStore(store);
  const currentProjectId = computed(() => forgeonConfig.currentProjectId);
  const { data: ruleResponse, loading: ruleListLoading, execute: getRuleList } = useLatestPromise(mergeApi.v1.ruleServiceGetRules);
  const { data: defaultFormRes, execute: getDefaultForm } = useLatestPromise(mergeApi.v1.ruleServiceGetDefaultRule);
  const { data: updateDefaultFormRes, execute: updateDefaultForm } = useLatestPromise(mergeApi.v1.ruleServiceUpdateDefaultRule);
  const { data: streamResponse, execute: getStreamList } = useLatestPromise(mergeApi.v1.ruleServiceGetStreams);
  const { data: createRuleRes, execute: createRule } = useLatestPromise(mergeApi.v1.ruleServiceCreateRule);
  const { data: updateRuleRes, execute: updateRule } = useLatestPromise(mergeApi.v1.ruleServiceUpdateRule);
  const { data: deleteRuleRes, execute: deleteRule } = useLatestPromise(mergeApi.v1.ruleServiceRemoveRule);
  const { execute: setRuleEnable } = useLatestPromise(mergeApi.v1.ruleServiceUpdateRuleEnable);
  const { execute: getFeishuProjectItem } = useLatestPromise(mergeApi.v1.ruleServiceGetFeishuProjectItem);

  /**
   * 飞书项目工作项标签映射
   */
  const feishuTagMap = ref<Record<FeishuWorkItem, RuleV1Field[]>>();

  const currentBranchMap = computed(() => {
    const depots = (streamResponse.value?.data?.data?.depots ?? []) as RuleV1Depot[];
    const map = new Map<number, RuleV1Stream & { depot: string }>();
    depots.forEach((depot) => {
      depot.streams?.forEach((stream) => {
        if (!stream.id) {
          return;
        }
        map.set(stream.id, { ...stream, depot: depot.name! });
      });
    });
    return map;
  });

  const refreshFeishuTagMap = async () => {
    const [resStory, resIssue] = await Promise.all([
      getFeishuProjectItem({ id: currentProjectId.value!, workItemType: FeishuWorkItem.story }, {}),
      getFeishuProjectItem({ id: currentProjectId.value!, workItemType: FeishuWorkItem.issue }, {}),
    ]);
    if (resStory?.data?.code === 0 && resIssue?.data?.code === 0) {
      feishuTagMap.value = {
        [FeishuWorkItem.story]: resStory?.data.data?.fields ?? [],
        [FeishuWorkItem.issue]: resIssue?.data.data?.fields ?? [],
      };
    }
  };

  const initHomeData = async () => {
    if (!currentProjectId.value) {
      return;
    }
    await Promise.all([
      getRuleList({ id: currentProjectId.value }, {}),
      getDefaultForm({ id: currentProjectId.value }, {}),
      getStreamList({ id: currentProjectId.value }, {}),
    ]);
  };

  function refreshRuleList() {
    if (!currentProjectId.value) {
      return;
    }
    return getRuleList({ id: currentProjectId.value }, {});
  }

  const onUpdateDefaultForm = async (params: RuleV1Rule) => {
    if (!currentProjectId.value) {
      return;
    }
    await updateDefaultForm({ id: currentProjectId.value }, {
      rule: params,
    });
    if (updateDefaultFormRes.value?.data?.code === 0) {
      await initHomeData();
      return true;
    }
    return false;
  };

  const onCreateRule = async (params: RuleV1Rule): Promise<boolean> => {
    if (!currentProjectId.value) {
      return false;
    }
    const res = await createRule({ id: currentProjectId.value }, {
      rule: params,
    });
    traceCustomEvent(TrackEventName.conflux_rule_create, {
      conflux_rule_id: res?.data?.data?.ruleId,
      conflux_source_branch_path: params.sourceStreamId ? currentBranchMap.value.get(params.sourceStreamId)?.path : undefined,
      conflux_target_branch_path: params.targetStreamId ? currentBranchMap.value.get(params.targetStreamId)?.path : undefined,
      conflux_trigger_type: params.mergeTrigger === RuleV1MergeTrigger.AUTO
        ? 'auto'
        : params.mergeTrigger === RuleV1MergeTrigger.TAG ? 'tag' : undefined,
    });
    if (createRuleRes.value?.data?.code === 0) {
      await getRuleList({ id: currentProjectId.value }, {});
      return true;
    }
    return false;
  };

  const onUpdateRule = async (params: RuleV1Rule): Promise<boolean> => {
    if (!currentProjectId.value || !params.id) {
      return false;
    }
    await updateRule({ id: currentProjectId.value, ruleId: params.id }, {
      rule: params,
    });
    if (updateRuleRes.value?.data?.code === 0) {
      await getRuleList({ id: currentProjectId.value }, {});
      return true;
    }
    return false;
  };

  const onDeleteRule = async (ruleId: string) => {
    if (!currentProjectId.value) {
      return;
    }
    await deleteRule({ id: currentProjectId.value, ruleId }, {});
    if (deleteRuleRes.value?.data?.code === 0) {
      await getRuleList({ id: currentProjectId.value }, {});
    }
  };

  const onSetRuleEnable = async (ruleId: string, enable: boolean) => {
    if (!currentProjectId.value) {
      return;
    }
    await setRuleEnable({ id: currentProjectId.value, ruleId }, {
      enable,
    });
    await getRuleList({ id: currentProjectId.value }, {});
  };

  return {
    streamList: computed(() => streamResponse.value?.data?.data?.depots ?? []),
    defaultFormRule: computed(() => defaultFormRes.value?.data?.data?.rule),
    currentProjectId,
    ruleList: computed(() => ruleResponse.value?.data?.data?.rules ?? []),
    ruleListLoading,
    currentBranchMap,
    feishuTagMap,
    initHomeData,
    onUpdateDefaultForm,
    onCreateRule,
    refreshRuleList,
    onUpdateRule,
    onDeleteRule,
    refreshFeishuTagMap,
    onSetRuleEnable,
  };
});

export {
  useMergeHome,
};
