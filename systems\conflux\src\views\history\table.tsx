import Icon from '@ant-design/icons-vue';
import { type MergeV1MergeRecord, MergeV1MergeRecordOrderBy, MergeV1MergeState, MergeV1OrderDirection } from '@hg-tech/api-schema-merge';
import { BasicVxeTable, PlatformEnterPoint } from '@hg-tech/oasis-common';
import { Button, message, Pagination, Popover, Tooltip } from 'ant-design-vue';
import dayjs from 'dayjs';
import { type PropType, computed, defineComponent, ref, watch } from 'vue';
import type { VxeGridInstance, VxeGridListeners, VxeGridProps } from 'vxe-table';
import { MergeStateLabelMap, renderMergeStateIcon } from '../../models/config.model';
import { type UserTagInfo, UserTag } from '../../components/UserTag';
import { HistoryDrawer } from './history-drawer';
import { useRouter } from 'vue-router';
import { PermissionProvider } from '../../components/PermissionProvider';
import { MergePermission } from '../../constants/premission';
import { traceCustomEvent } from '../../services/track';
import { TrackEventName } from '../../constants/event';
import { useMergeHistory } from './use-merge-histroy';

import BasicStrokeHistory from '../../assets/svg/BasicStrokeHistory.svg?component';
import LogoStrokeSwarm from '../../assets/svg/LogoStrokeSwarm.svg?component';
import SystemStrokeTips from '../../assets/svg/SystemStrokeTips.svg?component';
import { ForgeonThemeCssVar } from '@hg-tech/forgeon-style';

const SortFieldMap: Partial<Record<string, MergeV1MergeRecordOrderBy>> = {
  cl: MergeV1MergeRecordOrderBy.MERGE_RECORD_ORDER_BY_CL,
  submitTime: MergeV1MergeRecordOrderBy.MERGE_RECORD_ORDER_BY_SUBMIT_TIME,
  state: MergeV1MergeRecordOrderBy.MERGE_RECORD_ORDER_BY_STATE,
  submitter: MergeV1MergeRecordOrderBy.MERGE_RECORD_ORDER_BY_SUBMITTER,
};

const HistoryTable = defineComponent({
  props: {
    ruleId: {
      type: String as PropType<string>,
      default: '',
    },
    data: {
      type: Array as PropType<MergeV1MergeRecord[]>,
      default: () => [],
    },
    page: {
      type: Number as PropType<number>,
      default: 1,
    },
    pageSize: {
      type: Number as PropType<number>,
      default: 20,
    },
    total: {
      type: Number as PropType<number>,
      default: 0,
    },
    loading: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    onPageChange: {
      type: Function as PropType<(page: number, size: number) => void>,
    },
    onSortChange: {
      type: Function as PropType<(params: { field: MergeV1MergeRecordOrderBy; order: MergeV1OrderDirection }) => void>,
      default: () => {},
    },
  },
  emits: ['update:page', 'update:pageSize'],
  setup(props, { emit }) {
    const { currentBranchMap, ruleList } = useMergeHistory();
    const router = useRouter();
    const isShowHistoryDrawer = ref(false);
    const currentRow = ref<MergeV1MergeRecord>();
    const page = computed({
      get: () => props.page,
      set: (value: number) => {
        emit('update:page', value);
      },
    });
    const pageSize = computed({
      get: () => props.pageSize,
      set: (value: number) => {
        emit('update:pageSize', value);
      },
    });
    const historyTableRef = ref<VxeGridInstance<MergeV1MergeRecord>>();
    const tableRows = computed(() => props.data);
    const currentRule = computed(() => ruleList.value.find((rule) => rule.id === props.ruleId));
    watch(() => props.data, () => {
      if (historyTableRef.value) {
        setTimeout(() => {
          historyTableRef.value?.scrollTo(0, 0);
        }, 0);
      }
    }, { deep: true });

    const showHistoryDrawer = (row: MergeV1MergeRecord) => {
      currentRow.value = row;
      isShowHistoryDrawer.value = true;
    };

    const onCheckFiles = (row: MergeV1MergeRecord) => {
      traceCustomEvent(TrackEventName.conflux_go_to_swarm, {
        conflux_rule_id: currentRule.value?.id,
        conflux_source_branch_path: currentRule.value?.sourceStreamId ? currentBranchMap.value.get(currentRule.value?.sourceStreamId)?.path : undefined,
        conflux_target_branch_path: currentRule.value?.targetStreamId ? currentBranchMap.value.get(currentRule.value?.targetStreamId)?.path : undefined,
      });
      if (!row.swarmAddress) {
        message.warning('该记录没有提交文件地址');
        return;
      }
      window.open(row.swarmAddress, '_blank');
    };

    const renderState = (row: MergeV1MergeRecord) => {
      switch (row.state) {
        case MergeV1MergeState.MERGE_STATE_MERGE_CONFLICT:
        case MergeV1MergeState.MERGE_STATE_MERGE_FAILED:
        case MergeV1MergeState.MERGE_STATE_COMMIT_FAILED:
          return (
            <div class="flex gap-4px">
              <Tooltip title="去处理">
                <div
                  class="flex cursor-pointer gap-4px hover:c-FO-Content-Link-Hover"
                  onClick={() => {
                    const url = location.origin + router.resolve({
                      name: PlatformEnterPoint.ConfluxTask,
                      query: {
                        ruleId: props.ruleId,
                        clStart: row.cl,
                        clEnd: row.cl,
                      },
                    }).fullPath;
                    window.open(url, '_blank');
                  }}
                >
                  {renderMergeStateIcon[row.state as MergeV1MergeState]?.()}
                  <span>{MergeStateLabelMap[row.state as MergeV1MergeState] || '未知状态'}</span>
                </div>
              </Tooltip>
              {row.failDetail && (
                <Popover placement="topRight">
                  {{
                    default: () => (<Icon class="c-FO-Content-Icon2" component={<SystemStrokeTips />} />),
                    content: () => (
                      <>
                        <div class="FO-Font-R14 mb-8px flex items-center gap-8px c-FO-Content-Text1">
                          {renderMergeStateIcon[row.state as MergeV1MergeState]?.()}
                          <span>{MergeStateLabelMap[row.state as MergeV1MergeState] || '未知状态'}</span>
                        </div>
                        <div class="FO-Font-R12 max-h-200px max-w-500px overflow-y-auto whitespace-pre-wrap break-words pl-24px c-FO-Content-Text2 line-height-[16px]">
                          <div>{row.failDetail || '暂无详细信息'}</div>
                        </div>
                      </>
                    ),
                  }}
                </Popover>
              ) }
            </div>
          );
        default:
          return (
            <div class="flex gap-4px">
              {renderMergeStateIcon[row.state as MergeV1MergeState]?.()}
              <span>{MergeStateLabelMap[row.state as MergeV1MergeState] || '未知状态'}</span>
            </div>
          );
      }
    };

    const tableColumns = computed<VxeGridProps<MergeV1MergeRecord>['columns']>(() => [
      { type: null, width: 40, resizable: false },
      { field: 'cl', title: 'CL号', width: 100, sortable: true },
      { field: 'submitTime', title: '提交时间', width: 180, sortable: true, slots: {
        default({ row }) {
          return row.submitTime ? dayjs(Number.parseInt(row.submitTime) * 1000).format('YYYY-MM-DD HH:mm:ss') : '';
        },
      } },
      { field: 'description', title: '提交描述', minWidth: 200, showOverflow: 'tooltip', slots: {
        default({ row }) {
          return row.description || '--';
        },
      } },
      { field: 'submitter', title: '提交人', sortable: true, width: 200, slots: {
        default({ row }) {
          const user: UserTagInfo = {
            openId: row.submitter?.feishuOpenId || '',
            name: row.submitter?.name || row.submitter?.hgAccount || '--',
            avatar: row.submitter?.avatar || '',
            nickname: row.submitter?.nickname || '',
          };
          return <UserTag avatarSize={24} user={user} />;
        },
      } },
      { field: 'state', title: '状态', sortable: true, width: 150, slots: {
        default({ row }) {
          return renderState(row);
        },
      } },
      {
        field: 'actions',
        title: '操作',
        width: 120,
        fixed: 'right',
        minWidth: 120,
        slots: {
          default({ row }) {
            return (
              <div class="flex items-center gap-4px">
                <PermissionProvider permission={{ any: [MergePermission.ShowSwarm] }}>
                  <Tooltip title="查看提交文件">
                    <Button
                      class="btn-fill-text"
                      icon={<Icon class="font-size-14px c-FO-Content-Icon1" component={<LogoStrokeSwarm />} />}
                      onClick={() => onCheckFiles(row)}
                    />
                  </Tooltip>
                </PermissionProvider>
                <PermissionProvider permission={{ any: [MergePermission.ViewUserAction] }}>
                  <Tooltip title="查看操作历史">
                    <Button
                      class="btn-fill-text"
                      icon={<Icon class="font-size-14px c-FO-Content-Icon1" component={<BasicStrokeHistory />} />}
                      onClick={() => showHistoryDrawer(row)}
                    />
                  </Tooltip>
                </PermissionProvider>
              </div>
            );
          },
        },
      },
    ]);

    const gridOptions = computed(() => ({
      rowConfig: {
        keyField: 'id',
        isHover: true,
      },
      sortConfig: {
        remote: true,
      },
      height: '100%',
      loading: props.loading,
      columns: tableColumns.value,
      data: tableRows.value,
    }) as VxeGridProps);

    const gridEvent: VxeGridListeners<MergeV1MergeRecord> = ({
      sortChange({ field, order }) {
        props.onSortChange({
          field: SortFieldMap[field] || MergeV1MergeRecordOrderBy.MERGE_RECORD_ORDER_BY_INVALID,
          order: order === 'asc' ? MergeV1OrderDirection.ORDER_DIRECTION_ASC : MergeV1OrderDirection.ORDER_DIRECTION_DESC,
        });
      },
    });

    return () => (
      <div class="history-table flex flex-1 flex-col overflow-hidden">
        <div class="mb-24px flex-1">
          <BasicVxeTable
            cssVarOverrides={{
              '--vxe-ui-layout-background-color': ForgeonThemeCssVar.ContainerFill1,
            }}
            events={gridEvent}
            options={gridOptions.value}
            ref={historyTableRef}
          />
        </div>
        <div class="mb-12px flex items-center justify-end">
          <Pagination
            onChange={props.onPageChange}
            showSizeChanger
            showTotal={(total, range) => `显示 ${range[0]} - ${range[1]} 条，共 ${total} 条`}
            total={props.total}
            v-model:current={page.value}
            v-model:pageSize={pageSize.value}
          />
        </div>
        <HistoryDrawer cl={currentRow.value?.cl} id={currentRow.value?.id} v-model:visible={isShowHistoryDrawer.value} />
      </div>
    );
  },
});

export {
  HistoryTable,
};
