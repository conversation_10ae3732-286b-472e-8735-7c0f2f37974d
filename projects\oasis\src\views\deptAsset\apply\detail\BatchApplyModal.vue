<template>
  <Modal
    :width="600" :open="show" :maskClosable="false" destroyOnClose centered :afterClose="modalDestroy"
    wrapClassName="batch-apply-modal" @cancel="() => modalCancel()"
  >
    <template #title>
      <div class="text-start">
        <span class="font-size-[16px] font-bold">
          <span>{{ title }}</span>
        </span>
      </div>
    </template>

    <div>
      <Form ref="formRef" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }" labelAlign="left">
        <FormItem label="借出设备" name="deviceList" v-bind="validateInfos.deviceList" :colon="false">
          <div>
            <span>{{ deviceList.slice(0, 3).map((item) => item.deviceName || '').join('、') }}</span>
            <span v-if="deviceList.length > 2">等{{ deviceList.length }}台</span>
          </div>
        </FormItem>
        <FormItem label="借出人" v-bind="validateInfos.proposerID" :colon="false">
          <UserSelect v-model:value="modelRef.proposerID" placeholder="请选择干员，可拼音首字母搜索" class="w-260px!" />
        </FormItem>
        <FormItem label="预计归还日期" v-bind="validateInfos.returnTime" :colon="false">
          <div class="flex flex-col gap-2">
            <DatePicker
              v-model:value="modelRef.returnTime" placeholder="请选择预计归还日期" class="w-260px"
              :disabled="isInfinite" :disabledDate="disabledDate"
            />
            <FormItemRest>
              <Checkbox v-model:checked="isInfinite" class="w-fit" @change="modelRef.returnTime = undefined">
                长期借用
              </Checkbox>
            </FormItemRest>
          </div>
        </FormItem>
      </Form>
    </div>
    <template #footer>
      <div class="mt flex justify-end">
        <Button @click="() => modalCancel()">
          取消
        </Button>
        <Button type="primary" class="ml-2" @click="handleConfirm">
          借出
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { Button, Checkbox, DatePicker, Form, FormItem, Modal } from 'ant-design-vue';
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import type { NullableBasicResult } from '/@/api/model/baseModel';
import { reactive, ref } from 'vue';
import { UserSelect } from '/@/components/Form';
import type { DeviceListItem } from '/@/api/page/model/deptAssetModel';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';

const props = defineProps<ModalBaseProps<{ updatedItem?: NullableBasicResult }> & {
  title: string;
  deviceList: DeviceListItem[];
  sentReq?: (formValue: { proposerID?: number; returnTime?: number }) => Promise<NullableBasicResult | undefined>;
}>();
const useForm = Form.useForm;
const formRef = ref();
const isInfinite = ref(false);
const rulesRef = reactive({
  proposerID: [
    {
      required: true,
      message: '请选择借出人',
    },
  ],
  returnTime: [

    {
      required: true,
      validator: (_, value) => {
        if (!value && !isInfinite.value) {
          return Promise.reject(new Error('请选择归还时间'));
        }
        return Promise.resolve();
      },
      message: '请选择归还时间',
    },
  ],

});
const modelRef = ref<{ proposerID?: number; returnTime?: Dayjs }>({
  proposerID: undefined,
  returnTime: undefined,
});
const { validate, validateInfos } = useForm(modelRef, rulesRef, {});
function disabledDate(current: Dayjs) {
  return current && current < dayjs().startOf('day');
}
async function handleConfirm() {
  await validate();
  let time: number = 0;
  if (isInfinite.value) {
    time = -1;
  } else {
    time = dayjs(modelRef.value.returnTime).unix();
  }
  const updatedItem = await props.sentReq?.({ ...modelRef.value, returnTime: time });
  return props.modalConfirm({ updatedItem });
}
</script>

<style lang="less">
.batch-apply-modal {
  .ant-modal-content {
    padding: 0 !important;
  }
  .ant-modal-header {
    border-bottom: 1px solid var(--FO-Container-Stroke1);
    padding: 8px 16px;
    max-height: 40px;
  }
  .ant-modal-body {
    padding: 14px;
    padding-bottom: 20px;
  }
  .ant-modal-footer {
    padding: 14px;
    padding-top: 0px;
    margin-top: 0px;
  }
  .ant-modal-close {
    top: 9px !important;
  }
}
</style>
