import { computed, defineComponent, watch } from 'vue';
import { useMergeTask } from './use-merge-task';
import { Popover, Select } from 'ant-design-vue';
import { RuleV1StreamType } from '@hg-tech/api-schema-merge';
import { FilterWrapper } from './filter-wrapper';
import { TaskTable } from './table';
import { renderBranchTypeIcon } from '../../models/config.model';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import { groupBy } from 'lodash';
import Icon from '@ant-design/icons-vue';
import SystemFillCodebase from '../../assets/svg/SystemFillCodebase.svg?component';
import StreamRuleArrow from '../../assets/svg/StreamRuleArrow.svg?component';
import { useSyncToRouteQuery } from '../../composables/useSyncToRouteQuery.ts';
import { TaskGroupButton } from './task-group-button.tsx';
import { useForgeonConfigStore } from '../../store/modules/forgeonConfig.ts';
import { store } from '../../store/pinia.ts';
import { PermissionProvider } from '../../components/PermissionProvider.tsx';
import { MergePermission } from '../../constants/premission.ts';

const MergeTask = defineComponent({
  setup() {
    const {
      streamList,
      searchForm,
      taskTotal,
      taskList,
      ruleList,
      currentBranchMap,
      currentRuleId,
      taskLoading,
      fetchTaskList,
    } = useMergeTask();
    const forgeonConfig = useForgeonConfigStore(store);
    const currentProjectId = computed(() => forgeonConfig.currentProjectId);
    const currentRule = computed(() => ruleList.value.find((rule) => rule.id === currentRuleId.value));
    const ruleListByDepot = computed(() => {
      const depotMap = groupBy(ruleList.value, (rule) => rule.depot);
      return Object.entries(depotMap).map(([depot, rules]) => ({
        label: streamList.value.find((stream) => stream.path === depot)?.name,
        children: rules,
      }));
    });

    useSyncToRouteQuery(
      searchForm,
      (query, oldQuery) => ({
        name: PlatformEnterPoint.ConfluxTask,
        query: { ...oldQuery, ...query },
      }),
      { pick: ['clStart', 'clEnd', 'submitTimeStart', 'submitTimeEnd', 'failReason'] },
    );
    watch([
      currentRuleId,
      ...Object.keys(searchForm.value)
        .filter((i) => !['page', 'pageSize'].includes(i))
        .map((key) => () => searchForm.value[key as keyof typeof searchForm.value]),
    ], () => {
      searchForm.value.page = 1;
      fetchTaskList();
    }, { immediate: true });

    const renderRuleItem = (streamId: number) => {
      const branch = currentBranchMap.value.get(streamId!);
      return (
        <Popover placement="top" z-index={2000}>
          {{
            default: () => (
              <div class="branch-item flex flex-1 items-center gap-4px truncate">
                {renderBranchTypeIcon[branch?.streamType ?? RuleV1StreamType.DEVELOPMENT]()}
                <div class="branch-item-name FO-Font-B14">{branch?.name || '--'}</div>
                <div class="FO-Font-R14 truncate">({branch?.path || '--'})</div>
              </div>
            ),
            title: () => (
              <div class="branch-info">
                <div class="branch-item-status-text FO-Font-B14 flex items-center gap-4px">
                  {renderBranchTypeIcon[branch?.streamType ?? RuleV1StreamType.DEVELOPMENT]()}
                  {branch?.name || '--'}
                </div>
                <div class="branch-item-status-text FO-Font-R14">
                  {branch?.path}
                </div>
              </div>
            ),
          }}
        </Popover>
      );
    };

    return () => (
      <div class="merge-task flex flex-1 flex-col overflow-hidden">
        <div class="mb-12px rd-12px bg-FO-Container-Fill1 p-20px">
          <div class="flex items-center gap-12px">
            <span class="FO-Font-R14 whitespace-nowrap">选择合并规则</span>
            <div class="flex-1 overflow-hidden">
              <Select class="w-full" placeholder="请选择合并规则" v-model:value={currentRuleId.value}>
                {ruleListByDepot.value.map((dep) => (
                  <Select.OptGroup key={dep.label}>
                    {{
                      label: () => (
                        <div class="FO-Font-B14 flex items-center gap-4px c-FO-Content-Text1">
                          <Icon class="font-size-16px c-FO-Content-Icon2" component={<SystemFillCodebase />} />
                          {dep.label}
                        </div>
                      ),
                      default: () => (
                        <>{dep.children.map((rule) => (
                          <Select.Option key={rule.id} value={rule.id}>
                            <div class="h-full flex items-center gap-4px">
                              {renderRuleItem(rule.sourceStreamId!)}
                              <StreamRuleArrow class="mx-8px w-62px flex-shrink-0" />
                              {renderRuleItem(rule.targetStreamId!)}
                            </div>
                          </Select.Option>
                        ))}
                        </>
                      ),

                    }}

                  </Select.OptGroup>
                ))}
              </Select>
            </div>
          </div>
        </div>

        <div class="flex flex-1 flex-col overflow-hidden rd-12px bg-FO-Container-Fill1 p-20px">
          <div class="mb-24px">
            <div class="FO-Font-B16">失败的合并列表</div>
          </div>
          <div class="flex justify-between">
            <FilterWrapper
              class="mb-12px"
              v-model:form={searchForm.value}
            />
            <PermissionProvider permission={{ any: [MergePermission.CreateGroupChat] }}>
              <TaskGroupButton
                projectId={currentProjectId.value}
                ruleId={currentRuleId.value}
                sourcePath={currentRule.value?.sourceStreamId ? currentBranchMap.value.get(currentRule.value?.sourceStreamId)?.path : undefined}
                targetPath={currentRule.value?.targetStreamId ? currentBranchMap.value.get(currentRule.value?.targetStreamId)?.path : undefined}
              />
            </PermissionProvider>
          </div>
          <TaskTable
            data={taskList.value}
            loading={taskLoading.value}
            onPageChange={(page, size) => {
              searchForm.value.page = page;
              searchForm.value.pageSize = size;
              return fetchTaskList();
            }}
            onRefresh={fetchTaskList}
            onSortChange={async ({ field, order }) => {
              searchForm.value.orderBy = field;
              searchForm.value.orderDirection = order;
              searchForm.value.page = 1;
            }}
            total={taskTotal.value}
            v-model:page={searchForm.value.page}
            v-model:pageSize={searchForm.value.pageSize}
          />
        </div>
      </div>
    );
  },
});
export {
  MergeTask,
};
