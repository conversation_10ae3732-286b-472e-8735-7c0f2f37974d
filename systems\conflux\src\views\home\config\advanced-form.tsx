import { But<PERSON>, Collapse, Form, FormItem, InputNumber, Select, Switch, Tooltip } from 'ant-design-vue';
import { type MergeAdvancedRuleForm, MergeRuleFormPanelKey, MergeRuleOnlineProcessFileTypeOptions, MergeRuleOptions, RetryIntervalMinuteOptions } from '../../../models/config.model';
import { type PropType, type Ref, computed, defineComponent, nextTick, ref, watch } from 'vue';
import type { Rule } from 'ant-design-vue/es/form';
import Icon from '@ant-design/icons-vue';
import { useMergeHome } from '../use-merge-home';
import { isEqual } from 'lodash';
import { ForgeonUserSelector } from '@hg-tech/oasis-common';
import { useUserListOption } from '../../../composables/useUserSearch';
import { CustomRuleItem } from './custom-rule-item';
import { ExcludeRuleItem } from './exclude-rule-item';
import type { RuleV1Rule } from '@hg-tech/api-schema-merge';
import { useSortable } from '@vueuse/integrations/useSortable';
import type { SortableEvent } from 'sortablejs';

import ForwardArrow from '../../../assets/svg/ForwardArrow.svg?component';
import Add from '../../../assets/svg/Add.svg?component';
import DragStyle from '../../../components/Dragable.module.less';
import { FeishuRuleItem } from './feishu-rule-item';
import { AdditionResolverItem } from './addition-resolver-item';

function isFieldsChanged<T extends Record<string, any>>(
  prev: T,
  next: T,
  keys: (keyof T)[],
): boolean {
  return keys.some((key) => !isEqual(prev[key], next[key]));
}

const AdvancedForm = defineComponent({
  props: {
    isEdit: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    form: {
      type: Object as PropType<MergeAdvancedRuleForm>,
      default: () => ({}),
    },
    rules: {
      type: Object as PropType<Record<string, Rule[]>>,
      default: () => ({}),
    },
    activeKey: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    formRef: {
      type: Object as PropType<Ref<any>>,
      default: () => ({}),
    },
    onCollapseBasic: {
      type: Function as PropType<() => void>,
      default: () => () => {},
    },
    onSaveDefault: {
      type: Function as PropType<() => void>,
      default: () => () => {},
    },
    onResetValidate: {
      type: Function as PropType<() => void>,
      default: () => () => {},
    },
    tagOptions: {
      type: Array as PropType<{ label: string; value: string }[]>,
      default: () => [],
    },
  },
  emits: ['update:activeKey', 'update:form'],
  setup(props, { emit }) {
    const excludeRulesContainer = ref<HTMLElement>();
    const customResolveRulesContainer = ref<HTMLElement>();
    const feishuRulesContainer = ref<HTMLElement>();
    const additionalHandlersContainer = ref<HTMLElement>();
    const { defaultFormRule } = useMergeHome();
    const { userListOptions, userListLoading, queryUser, resetUserList } = useUserListOption(
      computed(() => [
        ...props.form.excludeUserUsers || [],
        ...props.form.defaultResolverUsers || [],
        ...defaultFormRule.value?.defaultResolverUsers || [],
        ...defaultFormRule.value?.excludeUserUsers || [],
      ]),
    );

    const advancedForm = computed({
      get: () => props.form,
      set: (value) => {
        emit('update:form', value);
      },
    });
    const activeKey = computed({
      get: () => props.activeKey,
      set: (value) => {
        emit('update:activeKey', value);
      },
    });
    const isDefaultRuleExist = computed(() => defaultFormRule.value);
    const isDefaultRuleChange = computed(() => {
      if (!isDefaultRuleExist.value) {
        return false;
      }

      return isFieldsChanged(defaultFormRule.value!, props.form, [
        'maxFiles',
        'excludeUsers',
        'defaultResolveRule',
        'defaultResolvers',
        'enableFeishuComment',
        'ignoredTags',
        'useCommitTool',
        'updateSubmitter',
        'onlineProcessFileTypes',
        'autoRetryEnable',
        'retryIntervalMinute',
        'excludeFeishuItem',
      ]);
    });
    const tagsOptions = computed(() => {
      const allOptions = [...props.tagOptions, ...(props.form.ignoredTags?.map((tag) => ({ label: tag, value: tag })) ?? [])];
      const uniqueMap = new Map<string, { label: string; value: string }>();
      allOptions.forEach((opt) => {
        if (!uniqueMap.has(opt.value)) {
          uniqueMap.set(opt.value, opt);
        }
      });
      return Array.from(uniqueMap.values());
    });

    const { start: startFeishuRulesSort } = useSortable(feishuRulesContainer, advancedForm.value.excludeFeishuItem!, {
      handle: '.drag-handle',
      animation: 300,
      direction: 'vertical',
      dragClass: DragStyle.dragGhost,
      ghostClass: DragStyle.dragGhost,
      onEnd: async (evt: SortableEvent) => {
        const { oldIndex, newIndex } = evt;
        if (oldIndex === newIndex || oldIndex === undefined || newIndex === undefined) {
          return;
        }
        const movedItem = advancedForm.value.excludeFeishuItem?.splice(oldIndex, 1)[0];
        movedItem && advancedForm.value.excludeFeishuItem?.splice(newIndex, 0, movedItem!);
        setTimeout(() => {
          props.onResetValidate();
        }, 100);
      },
    });

    const { start: startExcludeRulesSort } = useSortable(excludeRulesContainer, advancedForm.value.excludeRules!, {
      handle: '.drag-handle',
      animation: 300,
      direction: 'vertical',
      dragClass: DragStyle.dragGhost,
      ghostClass: DragStyle.dragGhost,
      onEnd: async (evt: SortableEvent) => {
        const { oldIndex, newIndex } = evt;
        if (oldIndex === newIndex || oldIndex === undefined || newIndex === undefined) {
          return;
        }
        const movedItem = advancedForm.value.excludeRules?.splice(oldIndex, 1)[0];
        movedItem && advancedForm.value.excludeRules?.splice(newIndex, 0, movedItem!);
        setTimeout(() => {
          props.onResetValidate();
        }, 100);
      },
    });

    const { start: startCustomResolveRulesSort } = useSortable(customResolveRulesContainer, advancedForm.value.customResolveRules!, {
      handle: '.drag-handle',
      animation: 300,
      direction: 'vertical',
      dragClass: DragStyle.dragGhost,
      ghostClass: DragStyle.dragGhost,
      onEnd: async (evt: SortableEvent) => {
        const { oldIndex, newIndex } = evt;
        if (oldIndex === newIndex || oldIndex === undefined || newIndex === undefined) {
          return;
        }
        const movedItem = advancedForm.value.customResolveRules?.splice(oldIndex, 1)[0];
        movedItem && advancedForm.value.customResolveRules?.splice(newIndex, 0, movedItem!);
        setTimeout(() => {
          props.onResetValidate();
        }, 100);
      },
    });

    const { start: startAdditionalHandlersSort } = useSortable(additionalHandlersContainer, advancedForm.value.additionalHandlers!, {
      handle: '.drag-handle',
      animation: 300,
      direction: 'vertical',
      dragClass: DragStyle.dragGhost,
      ghostClass: DragStyle.dragGhost,
      onEnd: async (evt: SortableEvent) => {
        const { oldIndex, newIndex } = evt;
        if (oldIndex === newIndex || oldIndex === undefined || newIndex === undefined) {
          return;
        }
        const movedItem = advancedForm.value.additionalHandlers?.splice(oldIndex, 1)[0];
        movedItem && advancedForm.value.additionalHandlers?.splice(newIndex, 0, movedItem!);
        setTimeout(() => {
          props.onResetValidate();
        }, 100);
      },
    });

    const updateSortableContainer = () => {
      if (advancedForm.value.excludeRules?.length) {
        startExcludeRulesSort();
      }
      if (advancedForm.value.customResolveRules?.length) {
        startCustomResolveRulesSort();
      }
      if (advancedForm.value.excludeFeishuItem?.length) {
        startFeishuRulesSort();
      }
      if (advancedForm.value.additionalHandlers?.length) {
        startAdditionalHandlersSort();
      }
    };

    watch(() => defaultFormRule.value!, (newVal: RuleV1Rule) => {
      if (newVal && !props.isEdit) {
        advancedForm.value = {
          initialCl: Number(newVal.initialCl) > 0 ? newVal.initialCl : undefined,
          excludeUsers: newVal.excludeUsers || [],
          defaultResolvers: newVal.defaultResolvers || [],
          defaultResolveRule: newVal.defaultResolveRule,
          ignoredTags: newVal.ignoredTags,
          enableFeishuComment: newVal.enableFeishuComment,
          maxFiles: newVal.maxFiles,
          useCommitTool: newVal.useCommitTool,
          autoRetryEnable: newVal.autoRetryEnable,
          retryIntervalMinute: newVal.retryIntervalMinute || undefined,
          excludeRules: newVal.excludeRules?.map((item) => ({
            ...item,
            id: Math.random().toString(36).substring(2, 15),
          })) || [{
            excludeMergeType: undefined,
            ruleType: undefined,
            pattern: '',
            id: Math.random().toString(36).substring(2, 15),
          }],
          customResolveRules: newVal.customResolveRules?.map((item) => ({
            ...item,
            id: Math.random().toString(36).substring(2, 15),
          })) || [{
            id: Math.random().toString(36).substring(2, 15),
            ruleType: undefined,
            pattern: '',
            resolveRule: undefined,
          }],
          excludeFeishuItem: newVal.excludeFeishuItem?.map((item) => ({
            ...item,
            id: Math.random().toString(36).substring(2, 15),
          })) || [{
            id: Math.random().toString(36).substring(2, 15),
            workItemType: undefined,
            fieldKey: undefined,
            optionValue: undefined,
          }],
          additionalHandlers: newVal.AdditionalHandlers?.map((item) => ({
            ...item,
            id: Math.random().toString(36).substring(2, 15),
          })) || [{
            id: Math.random().toString(36).substring(2, 15),
            operation: undefined,
            field: undefined,
            user: undefined,
            handler: undefined,
            userUser: undefined,
            handlerUser: [],
          }],
          onlineProcessFileTypes: newVal.onlineProcessFileTypes || [],
          updateSubmitter: newVal.updateSubmitter,
        };
      } else if (!newVal && !props.isEdit) {
        advancedForm.value = {
          initialCl: undefined,
          excludeUsers: [],
          defaultResolvers: [],
          defaultResolveRule: undefined,
          ignoredTags: [],
          enableFeishuComment: false,
          maxFiles: 1000,
          useCommitTool: false,
          autoRetryEnable: false,
          retryIntervalMinute: undefined,
          excludeRules: [{
            id: Math.random().toString(36).substring(2, 15),
            excludeMergeType: undefined,
            ruleType: undefined,
            pattern: '',
          }],
          customResolveRules: [{
            id: Math.random().toString(36).substring(2, 15),
            ruleType: undefined,
            pattern: '',
            resolveRule: undefined,
          }],
          excludeFeishuItem: [{
            id: Math.random().toString(36).substring(2, 15),
            workItemType: undefined,
            fieldKey: undefined,
            optionValue: undefined,
          }],
          additionalHandlers: [{
            id: Math.random().toString(36).substring(2, 15),
            operation: undefined,
            field: undefined,
            user: undefined,
            handler: undefined,
            userUser: undefined,
            handlerUser: [],
          }],
          onlineProcessFileTypes: [],
          updateSubmitter: false,
        };
      }
    }, { immediate: true, deep: true });

    watch([advancedForm.value.excludeRules, advancedForm.value.customResolveRules, advancedForm.value.excludeFeishuItem], () => {
      nextTick(() => {
        updateSortableContainer();
      });
    }, { deep: true, immediate: true });

    const onAdd = (type: 'exclude' | 'custom' | 'feishu' | 'additional', index: number) => {
      switch (type) {
        case 'exclude':
          advancedForm.value.excludeRules?.splice(index + 1, 0, {
            id: Math.random().toString(36).substring(2, 15),
            excludeMergeType: undefined,
            ruleType: undefined,
            pattern: '',
          });
          break;
        case 'custom':
          advancedForm.value.customResolveRules?.splice(index + 1, 0, {
            id: Math.random().toString(36).substring(2, 15),
            ruleType: undefined,
            pattern: '',
            resolveRule: undefined,
          });
          break;
        case 'feishu':
          advancedForm.value.excludeFeishuItem?.splice(index + 1, 0, {
            id: Math.random().toString(36).substring(2, 15),
            workItemType: undefined,
            fieldKey: undefined,
            optionValue: undefined,
          });
          break;
        case 'additional':
          advancedForm.value.additionalHandlers?.splice(index + 1, 0, {
            id: Math.random().toString(36).substring(2, 15),
            operation: undefined,
            field: undefined,
            user: undefined,
            handler: undefined,
          });
      }
    };

    const onRemove = (type: 'exclude' | 'custom' | 'feishu' | 'additional', index: number) => {
      switch (type) {
        case 'exclude':
          advancedForm.value.excludeRules?.splice(index, 1);
          break;
        case 'custom':
          advancedForm.value.customResolveRules?.splice(index, 1);
          break;
        case 'feishu':
          advancedForm.value.excludeFeishuItem?.splice(index, 1);
          break;
        case 'additional':
          advancedForm.value.additionalHandlers?.splice(index, 1);
      }
      props.onResetValidate();
    };

    return () => (
      <Form layout="vertical" model={props.form} ref={props.formRef} rules={props.rules}>
        <Collapse
          class="b-none"
          collapsible="icon"
          expandIcon={({ isActive }) => (
            <>

              <Button
                class="btn-fill-default flex items-center justify-center"
                icon={(
                  <Icon class="font-size-18px" component={<ForwardArrow class={isActive ? 'rotate-90' : ''} />} />
                )}
                type="text"
              />
            </>

          )}
          expandIconPosition="end"
          ghost
          onChange={(activeKey) => {
            if (Array.isArray(activeKey) && activeKey.length > 0) {
              props.onCollapseBasic();
            }
          }}
          v-model:activeKey={activeKey.value}
        >
          <Collapse.Panel
            header={(
              <div class="FO-Font-B16 flex items-center">
                <div class="whitespace-nowrap">
                  高级配置
                </div>

                <div class="w-full flex items-center justify-between">
                  <div>
                    {
                      isDefaultRuleExist.value && !isDefaultRuleChange.value && !props.isEdit && (
                        <span class="FO-Font-R12 ml-4px c-FO-Content-Text2">默认配置</span>
                      )
                    }
                  </div>
                  <div>
                    {
                      isDefaultRuleExist.value && isDefaultRuleChange.value && !props.isEdit && (
                        <div class="mr-8px">
                          <span class="FO-Font-R14 ml-4px c-FO-Content-Text1">默认配置发生更改，是否更新</span>
                          <span class="FO-Font-R14 ml-4px cursor-pointer c-FO-Brand-Primary-Default" onClick={props.onSaveDefault}>更新配置 </span>
                        </div>
                      )
                    }
                    {
                      !isDefaultRuleExist.value && !props.isEdit && (
                        <div class="mr-8px">
                          <span class="FO-Font-R14 ml-4px c-FO-Content-Text1">是否保存当前信息为默认配置</span>
                          <span class="FO-Font-R14 ml-4px cursor-pointer c-FO-Brand-Primary-Default" onClick={props.onSaveDefault}>新增配置 </span>
                        </div>
                      )
                    }
                  </div>
                </div>

              </div>
            )}
            key={MergeRuleFormPanelKey.Advanced}
          >
            <FormItem label={<span class="FO-Font-B14">合并初始 CL 号</span>} name="initialCl">
              <InputNumber
                class="w-full"
                disabled={props.isEdit}
                min={1}
                placeholder={props.isEdit ? '未填写' : '不填默认为最新 CL'}
                step={1}
                stringMode={true}
                v-model:value={advancedForm.value.initialCl}
              />
            </FormItem>
            <FormItem label={<span class="FO-Font-B14">不合并的标签（只需要填写[]内的文字）</span>} name="ignoredTags">
              <Select
                allowClear
                mode="tags"
                options={tagsOptions.value}
                placeholder="填写表示不合并的标签，保存配置时自动加入提交工具可选标签"
                v-model:value={advancedForm.value.ignoredTags}
              />
            </FormItem>
            <FormItem label={<span class="FO-Font-B14">忽略合并的用户</span>} name="excludeUsers">
              <ForgeonUserSelector
                loading={userListLoading.value}
                multiple
                onReset={resetUserList}
                onSearch={(params) => {
                  queryUser(params, {});
                }}
                options={userListOptions.value}
                placeholder="请输入用户姓名/昵称/邮箱/拼音"
                showAvatar
                v-model:value={advancedForm.value.excludeUsers}
              />
            </FormItem>
            <FormItem label={<span class="FO-Font-B14">默认冲突解决规则</span>} name="defaultResolveRule">
              <Tooltip title={
                advancedForm.value.defaultResolveRule
                  ? MergeRuleOptions.find((item) => item.value === advancedForm.value.defaultResolveRule)?.desc
                  : undefined
              }
              >
                <Select
                  allowClear
                  optionLabelProp="label"
                  placeholder="请选择默认冲突解决规则"
                  showSearch
                  v-model:value={advancedForm.value.defaultResolveRule}
                >
                  {MergeRuleOptions.map((item) => (
                    <Select.Option key={item.value} label={item.label} value={item.value}>
                      <div class="h-full">
                        <div class="FO-Font-R14 c-FO-Content-Text1">{item.label}</div>
                        <div class="FO-Font-R12 ml-4px c-FO-Content-Text2">({item.desc})</div>
                      </div>
                    </Select.Option>
                  ))}
                </Select>
              </Tooltip>
            </FormItem>
            <FormItem label={<span class="FO-Font-B14">默认冲突处理人</span>} name="defaultResolvers">
              <ForgeonUserSelector
                loading={userListLoading.value}
                multiple
                onReset={resetUserList}
                onSearch={(params) => {
                  queryUser(params, {});
                }}
                options={userListOptions.value}
                placeholder="请输入用户姓名/昵称/邮箱/拼音"
                showAvatar
                v-model:value={advancedForm.value.defaultResolvers}
              />
            </FormItem>
            <FormItem name="additionalHandlers">
              <div class="mb-8px w-full flex items-center justify-between">
                <span class="FO-Font-B14">附加处理人规则</span>
                {advancedForm.value.additionalHandlers && advancedForm.value.additionalHandlers.length <= 0 && (
                  <Button
                    class="btn-fill-default"
                    icon={(
                      <Icon component={<Add />} />
                    )}
                    onClick={() => onAdd('additional', 0)}
                    type="text"
                  />
                )}
              </div>
              <div ref={additionalHandlersContainer}>
                {
                  advancedForm.value.additionalHandlers?.map((item, index) => {
                    return item && (
                      <AdditionResolverItem
                        domain="additionalHandlers"
                        handlerList={item.handlerUser || []}
                        index={index}
                        key={`${item.id}-${index}`}
                        onAdd={(index) => onAdd('additional', index)}
                        onRemove={(index) => onRemove('additional', index)}
                        userList={item.userUser ? [item.userUser] : []}
                        v-model:field={item.field}
                        v-model:handler={item.handler}
                        v-model:operation={item.operation}
                        v-model:user={item.user}
                      />
                    );
                  },
                  )
                }
              </div>
            </FormItem>
            <FormItem name="enableFeishuComment">
              <div class="w-full flex items-center justify-between">
                <span class="FO-Font-B14 c-FO-Content-Text1">合并提交完成后添加飞书评论</span>
                <Switch v-model:checked={advancedForm.value.enableFeishuComment} />
              </div>
            </FormItem>
            <FormItem label={<span class="FO-Font-B14">文件合并最大数量</span>} name="maxFiles">
              <InputNumber class="w-full" placeholder="请输入最大数量" precision={0} v-model:value={advancedForm.value.maxFiles} />
            </FormItem>
            <FormItem name="excludeFeishuItem">
              <div class="mb-8px w-full flex items-center justify-between">
                <span class="FO-Font-B14">忽略合并规则（匹配飞书项目字段）</span>
                {advancedForm.value.excludeFeishuItem && advancedForm.value.excludeFeishuItem.length <= 0 && (
                  <Button
                    class="btn-fill-default"
                    icon={(
                      <Icon component={<Add />} />
                    )}
                    onClick={() => onAdd('feishu', 0)}
                    type="text"
                  />
                )}
              </div>
              <div ref={feishuRulesContainer}>
                {
                  advancedForm.value.excludeFeishuItem?.map((item, index) => {
                    return item && (
                      <FeishuRuleItem
                        domain="excludeFeishuItem"
                        index={index}
                        key={`${item.id}-${index}`}
                        onAdd={(index) => onAdd('feishu', index)}
                        onRemove={(index) => onRemove('feishu', index)}
                        v-model:feishuItemOperation={item.feishuItemOperation}
                        v-model:fieldKey={item.fieldKey}
                        v-model:optionValue={item.optionValue}
                        v-model:workItemType={item.workItemType}
                      />
                    );
                  })
                }
              </div>
            </FormItem>
            <FormItem name="excludeRules">
              <div class="mb-8px w-full flex items-center justify-between">
                <span class="FO-Font-B14">忽略合并规则（匹配源分支文件路径）</span>
                {advancedForm.value.excludeRules && advancedForm.value.excludeRules.length <= 0 && (
                  <Button
                    class="btn-fill-default"
                    icon={(
                      <Icon component={<Add />} />
                    )}
                    onClick={() => onAdd('exclude', 0)}
                    type="text"
                  />
                )}
              </div>
              <div ref={excludeRulesContainer}>
                {
                  advancedForm.value.excludeRules?.map((item, index) => {
                    return item && (
                      <ExcludeRuleItem
                        domain="excludeRules"
                        index={index}
                        key={`${item.id}-${index}`}
                        onAdd={(index) => onAdd('exclude', index)}
                        onRemove={(index) => onRemove('exclude', index)}
                        v-model:excludeMergeType={item.excludeMergeType}
                        v-model:pattern={item.pattern}
                        v-model:ruleType={item.ruleType}
                      />
                    );
                  })
                }
              </div>
            </FormItem>
            <FormItem name="customResolveRules">
              <div class="mb-8px w-full flex items-center justify-between">
                <span class="FO-Font-B14">特殊冲突解决规则（匹配源分支文件路径）</span>
                {advancedForm.value.customResolveRules && advancedForm.value.customResolveRules.length <= 0 && (
                  <Button
                    class="btn-fill-default"
                    icon={(
                      <Icon component={<Add />} />
                    )}
                    onClick={() => onAdd('custom', 0)}
                    type="text"
                  />
                )}
              </div>
              <div ref={customResolveRulesContainer}>
                {
                  advancedForm.value.customResolveRules?.map((item, index) => {
                    return item && (
                      <CustomRuleItem
                        domain="customResolveRules"
                        index={index}
                        key={`${item.id}-${index}`}
                        onAdd={(index) => onAdd('custom', index)}
                        onRemove={(index) => onRemove('custom', index)}
                        v-model:pattern={item.pattern}
                        v-model:resolveRule={item.resolveRule}
                        v-model:ruleType={item.ruleType}
                      />
                    );
                  },
                  )
                }
              </div>
            </FormItem>
            <FormItem name="useCommitTool">
              <div class="w-full flex items-center justify-between">
                <span class="FO-Font-B14 c-FO-Content-Text1">是否通过提交工具提交</span>
                <Switch v-model:checked={advancedForm.value.useCommitTool} />
              </div>
            </FormItem>
            <FormItem name="updateSubmitter">
              <div class="w-full flex items-center justify-between">
                <span class="FO-Font-B14 c-FO-Content-Text1">合并提交后将提交人改为原始提交人</span>
                <Switch v-model:checked={advancedForm.value.updateSubmitter} />
              </div>
            </FormItem>
            <FormItem label={<span class="FO-Font-B14">可用在线解决的文件类型</span>} name="onlineProcessFileTypes">
              <Select
                allowClear
                mode="multiple"
                options={MergeRuleOnlineProcessFileTypeOptions}
                placeholder="请选择在线处理文件类型"
                v-model:value={advancedForm.value.onlineProcessFileTypes}
              />
            </FormItem>
            <FormItem name="autoRetryEnable">
              <div class="w-full flex items-center justify-between">
                <span class="FO-Font-B14 c-FO-Content-Text1">是否启用自动重试（秒）</span>
                <Switch v-model:checked={advancedForm.value.autoRetryEnable} />
              </div>
            </FormItem>
            {
              advancedForm.value.autoRetryEnable && (
                <FormItem label={<span class="FO-Font-B14">自动重试时间</span>} name="retryIntervalMinute" required>
                  <Select
                    allowClear
                    placeholder="请选择时间间隔"
                    v-model:value={advancedForm.value.retryIntervalMinute}
                  >
                    {RetryIntervalMinuteOptions.map((option) => (
                      <Select.Option key={option.value} value={option.value}>
                        {option.label}
                      </Select.Option>
                    ))}
                  </Select>
                </FormItem>
              )
            }
          </Collapse.Panel>
        </Collapse>
      </Form>
    );
  },
});

export {
  AdvancedForm,
};
