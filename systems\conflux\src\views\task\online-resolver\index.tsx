import Icon from '@ant-design/icons-vue';
import { <PERSON><PERSON>, Drawer, message, Modal } from 'ant-design-vue';
import { type PropType, computed, defineComponent, ref, watch } from 'vue';
import { type MergeV1MergeRecord, type MergeV1ResolveConflict, MergeV1ResolveRule } from '@hg-tech/api-schema-merge';
import { useMergeTask } from '../use-merge-task';
import { FileTable } from './file-table';
import { DrawerHeader } from '../components/drawer-header';
import { isEqual } from 'lodash';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { mergeApi } from '../../../api';
import { getLocalStorage, MergeLocalStorageKey, setLocalStorage } from '../../../services/localstorge';
import { useCountdown } from '../../../services/countdown';
import { traceCustomEvent } from '../../../services/track';
import { TrackEventName } from '../../../constants/event';

import Close from '../../../assets/svg/Close.svg?component';
import BasicFillWarning from '../../../assets/svg/BasicFillWarning.svg?component';
import BasicStrokeSecurity from '../../../assets/svg/BasicStrokeSecurity.svg?component';

const OnlineResolver = defineComponent({
  props: {
    visible: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    data: {
      default: () => ({}),
      type: Object as PropType<MergeV1MergeRecord>,
    },
    onClose: {
      type: Function as PropType<() => void>,
      default: () => {},
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const { clearTaskFiles, fetchTaskFiles, fetchTaskList, fileList, currentProjectId, ruleList, currentRuleId, currentBranchMap } = useMergeTask();
    const { execute: updateResolveDetail, loading: updateLoading } = useLatestPromise(mergeApi.v1.mergeServiceUpdateResolveDetail);
    const { execute: onlineProcess, loading: onlineLoading } = useLatestPromise(mergeApi.v1.mergeServiceOnlineProcess);
    const { loading: countdownLoading, start: startCountdown, remainingTime } = useCountdown();
    const drawerRef = ref<HTMLDivElement>();
    const defaultFiles = ref<MergeV1ResolveConflict[]>([]);
    const isDataChanged = computed(() => {
      return !isEqual(defaultFiles.value, fileList.value);
    });
    const isFileAllHandled = computed(() => {
      if (defaultFiles.value.length === 0) {
        return false;
      }
      return defaultFiles.value.every((item) => item.resolveRule !== MergeV1ResolveRule.RESOLVE_RULE_INVALID);
    });
    const currentRule = computed(() => {
      return ruleList.value.find((rule) => rule.id === currentRuleId.value);
    });

    const open = computed({
      get: () => props.visible,
      set: (value) => {
        emit('update:visible', value);
      },
    });

    const onSave = async () => {
      if (defaultFiles.value.length === 0) {
        message.warning('没有需要保存的文件处理进度');
        return;
      }
      traceCustomEvent(TrackEventName.conflux_process_save, {
        conflux_rule_id: currentRuleId.value,
        conflux_source_branch_path: currentRule.value?.sourceStreamId ? currentBranchMap.value.get(currentRule.value.sourceStreamId)?.path : undefined,
        conflux_target_branch_path: currentRule.value?.targetStreamId ? currentBranchMap.value.get(currentRule.value.targetStreamId)?.path : undefined,
      });
      try {
        const res = await updateResolveDetail({
          id: currentProjectId.value!,
        }, {
          recordId: props.data.id,
          resolves: defaultFiles.value,
        });
        if (res?.data?.code !== 0) {
          message.error('保存失败，请稍后重试');
          return;
        }
        message.success('保存成功');
        fetchTaskList();
        open.value = false;
      } catch (error) {
        console.error('保存失败', error);
      }
    };

    const onSubmit = async () => {
      if (defaultFiles.value.length === 0) {
        message.warning('没有需要保存的文件处理进度');
        return;
      }
      traceCustomEvent(TrackEventName.conflux_online_resolve, {
        conflux_rule_id: currentRuleId.value,
        conflux_source_branch_path: currentRule.value?.sourceStreamId ? currentBranchMap.value.get(currentRule.value.sourceStreamId)?.path : undefined,
        conflux_target_branch_path: currentRule.value?.targetStreamId ? currentBranchMap.value.get(currentRule.value.targetStreamId)?.path : undefined,
      });
      try {
        const res = await updateResolveDetail({
          id: currentProjectId.value!,
        }, {
          recordId: props.data.id,
          resolves: defaultFiles.value,
        });
        if (res?.data?.code !== 0) {
          message.error('重试合并前保存失败，请稍后重试');
          return;
        }
        const retryRes = await onlineProcess({
          id: currentProjectId.value!,
        }, {
          recordId: props.data.id,
        });

        if (retryRes?.data?.code !== 0) {
          message.error('重试合并失败，请稍后重试');
          return;
        }
        message.success('重试合并成功');
        fetchTaskList();
        open.value = false;
      } catch (error) {
        message.error('重试合并失败，请稍后重试');
        console.error('重试合并失败', error);
      }
    };

    const onClose = () => {
      clearTaskFiles();
      props.onClose();
      open.value = false;
    };

    const onHandleClose = () => {
      if (isDataChanged.value) {
        const m = Modal.confirm({
          title: '数据暂未保存',
          content: '文件处理状态发生变更，退出后处理进度会丢失，要保存吗？',
          okText: '退出',
          cancelText: '留在当前',
          closable: true,
          footer: () => (
            <div class="flex items-center justify-end gap-8px">
              <Button
                class="btn-fill-default"
                onClick={() => {
                  m.destroy();
                  onClose();
                }}
                type="text"
              >退出
              </Button>
              <Button class="btn-fill-secondary" onClick={() => m.destroy()}>留在当前</Button>
            </div>
          ),
        });
      } else {
        onClose();
      }
    };

    const openResolveTip = (countdown: boolean = true) => {
      countdown && startCountdown(3);
      const m = Modal.warning({
        title: () => (
          <div class="flex items-center gap-8px">
            <Icon class="font-size-24px c-FO-Content-Icon1" component={<BasicStrokeSecurity />} />
            <span class="FO-Font-B16">在线处理冲突风险提示</span>
          </div>
        ),
        content: () => (
          <div class="FO-Font-R14 my-24px">
            <div class="c-FO-Content-Text2">
              在线处理冲突只能保留一边的修改:
            </div>
            <div class="c-FO-Content-Text2">
              <span class="FO-Font-B14 c-FO-Content-Text1">1. 选择保留目标分支的修改会导致源分支的文件没有被合并。</span>
            </div>
            <div class="c-FO-Content-Text2">
              <span class="FO-Font-B14 c-FO-Content-Text1">2. 选择接受源分支的修改会导致目标分支的文件被覆盖。</span>
            </div>
            <div class="c-FO-Content-Text2">如果某一边的提交人不是您,建议您和提交人充分沟通之后再执行操作。</div>
          </div>
        ),
        icon: null,
        width: 500,
        footer: () => (
          <div class="flex items-center justify-between">
            <a class="c-FO-Content-Link-Default" href="https://hypergryph.feishu.cn/wiki/SfRXwNmOqi1XJFkNWjHcqyxDnef" target="_blank">在线处理冲突操作手册</a>
            <Button
              class="btn-fill-primary"
              disabled={countdownLoading.value}
              loading={countdownLoading.value}
              onClick={() => {
                setLocalStorage(MergeLocalStorageKey.isOnlineResolverTipsShow, 'true');
                m.destroy();
              }}
              type="primary"
            >
              {
                countdownLoading.value
                  ? `我已知晓(${remainingTime.value}秒)`
                  : '我已知晓'
              }
            </Button>
          </div>
        ),
        bodyStyle: { padding: '0px' },
      });
    };

    const checkFirstOnlineResolver = () => {
      const isExist = getLocalStorage(MergeLocalStorageKey.isOnlineResolverTipsShow) === 'true';
      if (!isExist) {
        openResolveTip();
      }
    };

    watch(() => open.value, async () => {
      if (open.value && props.data && props.data.id) {
        await fetchTaskFiles(props.data.id);
        defaultFiles.value = fileList.value;
        checkFirstOnlineResolver();
      }
    }, { immediate: true, deep: true });

    return () => (
      <Drawer
        bodyStyle={{ padding: '24px', overflow: 'hidden' }}
        closable={false}
        destroyOnClose={true}
        mask={true}
        maskClosable={false}
        onClose={onHandleClose}
        placement="right"
        title="在线处理冲突"
        v-model:open={open.value}
        width={924}
      >
        {{
          extra: () => (
            <div class="flex items-center gap-12px">
              <Button
                class="flex items-center justify-center"
                icon={(
                  <Icon class="font-size-18px" component={<BasicStrokeSecurity />} />
                )}
                onClick={() => openResolveTip(false)}
                type="text"
              />

              <Button
                class="flex items-center justify-center"
                icon={(
                  <Icon class="font-size-18px" component={<Close />} />
                )}
                onClick={onHandleClose}
                type="text"
              />
            </div>

          ),
          default: () => (
            <div class="resolver-content h-full flex flex-col" id="online-resolver-drawer" ref={drawerRef}>
              <DrawerHeader cl={props.data?.cl} user={props.data?.submitter}>
                <div>
                  冲突处理进度：
                  <span class="FO-Font-B16">
                    {defaultFiles.value
                      .filter((item) => item.resolveRule !== MergeV1ResolveRule.RESOLVE_RULE_INVALID)
                      .length}
                    /
                    {defaultFiles.value.length}
                  </span>
                </div>
              </DrawerHeader>
              <FileTable class="flex-1 overflow-hidden" v-model:data={defaultFiles.value} />
            </div>
          ),
          footer: () => (
            <div class="flex items-center justify-end gap-8px">
              {isDataChanged.value && (
                <div
                  class="h-32px flex items-center gap-8px rd-6px bg-FO-Functional-Warning2-Default px-12px c-FO-Functional-Warning1-Default"
                >
                  <Icon class="font-size-16px" component={<BasicFillWarning />} />
                  当前有未保存数据
                </div>
              )}
              <Button
                class="btn-fill-secondary"
                loading={updateLoading.value}
                onClick={onSave}
              >保存进度
              </Button>
              <Button
                class="btn-fill-primary"
                disabled={!isFileAllHandled.value || updateLoading.value || onlineLoading.value}
                onClick={onSubmit}
                type="primary"
              >解决完成
              </Button>
            </div>
          ),
        }}
      </Drawer>
    );
  },
});

export {
  OnlineResolver,
};
