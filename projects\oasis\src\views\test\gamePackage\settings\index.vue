<template>
  <div :class="prefixCls">
    <div :class="`${prefixCls}__left`">
      <div :class="`${prefixCls}__left-back-btn`" @click="goBack()">
        <Icon icon="ph:caret-left-bold" :size="16" />
        返回游戏包体中心
      </div>
      <AppProjectSelect
        v-if="isFullScreen"
        theme="dark"
        outsideHeader
        popupBody
        class="mb-4"
      />
      <div :class="`${prefixCls}__left-tabs`">
        <div
          v-for="tab in tabList"
          :key="tab.name"
          :class="`${prefixCls}__left-tabs-item`"
          :active="curTab === tab.name"
          @click="tabClick(tab.name)"
        >
          <Icon :icon="tab.icon" class="mr-1" />
          {{ tab.name }}{{ tab.name === '回收站' ? `(${recycleTotal})` : '' }}
        </div>
      </div>
    </div>
    <div :class="`${prefixCls}__right`">
      <component :is="currentComponent" @success="getRecycleTotal" />
    </div>
  </div>
</template>

<script lang="ts" setup name="GamePackageSettings">
import { computed, onBeforeMount, ref } from 'vue';
import { useRouter } from 'vue-router';
import AutoDeleteSetting from './autoDelete/index.vue';
import BranchSetting from './branch/index.vue';
import DefaultFilterSetting from './defaultFilter/index.vue';
import TestConfigSetting from './testConfig/index.vue';
import RecycleBin from './recycleBin/index.vue';
import TagSetting from './tag/index.vue';
import { AppProjectSelect } from '/@/components/Application';
import Icon from '/@/components/Icon';
import { useDesign } from '/@/hooks/web/useDesign';
import { useGo } from '/@/hooks/web/usePage';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { getRecycleGamePackagesListByPage } from '/@/api/page/test';
import { sendEvent } from '/@/service/tracker/index.ts';
import { GamePackageCenterTrackTrigger } from '/@/api/page/model/testModel';
import shieldAddIcon from '@iconify-icons/icon-park-outline/shield-add';

const { prefixCls } = useDesign('game-package-settings');
const go = useGo();
const userStore = useUserStoreWithOut();
const { currentRoute } = useRouter();
const oasis = Number(currentRoute.value.query?.oasis || 0);
const isFullScreen = currentRoute.value.query?.fs === '1';
const recycleTotal = ref(0);
const tabList = [
  {
    name: '分支配置',
    component: BranchSetting,
    icon: 'tabler:layout-grid',
  },
  {
    name: '标签配置',
    component: TagSetting,
    icon: 'icon-park-outline:tag-one',
  },
  {
    name: '自动删除配置',
    component: AutoDeleteSetting,
    icon: 'icon-park-outline:delete',
  },
  {
    name: '默认筛选配置',
    component: DefaultFilterSetting,
    icon: 'icon-park-outline:filter',
  },
  {
    name: '包体检查配置',
    component: TestConfigSetting,
    icon: shieldAddIcon,
  },
  {
    name: '回收站',
    component: RecycleBin,
    icon: 'icon-park-outline:recycling',
  },
];

const curTab = ref<string>(tabList[0].name);

const currentComponent = computed(() => {
  return tabList.find((tab) => tab.name === curTab.value)!.component;
});

function tabClick(name: string) {
  curTab.value = name;
  const data = {
    game_package_center_trigger_channel: !oasis ? GamePackageCenterTrackTrigger.Web : GamePackageCenterTrackTrigger.Oasis,

  };
  switch (name) {
    case '标签配置':
      sendEvent('game_package_center_enter_tag_config', data);
      break;
    case '自动删除配置':
      sendEvent('game_package_center_enter_auto_delete', data);
      break;
    case '默认筛选配置':
      sendEvent('game_package_center_enter_default_filter', data);
      break;
    case '回收站':
      sendEvent('game_package_center_enter_recycle_bin', data);
      break;
  }
}

/** 获取回收站总数 */
async function getRecycleTotal() {
  if (!userStore.getProjectId) {
    return;
  }

  const { versionTotal } = await getRecycleGamePackagesListByPage(userStore.getProjectId, { page: 1, pageSize: 1 });

  recycleTotal.value = versionTotal || 0;
}

onBeforeMount(() => {
  getRecycleTotal();
});

function goBack() {
  go({ name: 'GamePackage' });
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-game-package-settings';
.@{prefix-cls} {
  display: flex;
  position: relative;
  overflow: auto;

  &__left {
    display: flex;
    position: sticky;
    top: 0;
    flex-direction: column;
    width: 200px;
    margin-left: 16px;
    padding: 16px 16px 16px 0;

    &-back-btn {
      margin-bottom: 16px;
      padding: 8px;
      border-radius: 8px;
      background-color: @FO-Container-Fill1;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      text-align: center;
    }

    &-tabs {
      &-item {
        display: flex;
        align-items: center;
        padding: 4px 8px;
        border-radius: 16px;
        font-weight: bold;
        cursor: pointer;
        user-select: none;

        &[active='true'],
        &:hover {
          background-color: @FO-Container-Fill1;
        }

        &:not(:last-child) {
          margin-bottom: 16px;
        }
      }
    }
  }

  &__right {
    flex: 1;
    width: 0;
    margin-right: 16px;
    padding: 16px 0;
  }
}
</style>import { getAllGamePackagesRecycleVersionsListByPage } from '/@/api/page/test'
import { useUserStoreWithOut } from '/@/store/modules/user'import { getAllGamePackagesRecycleVersionsListByPage } from '/@/api/page/test'
import { useUserStoreWithOut } from '/@/store/modules/user'import { getAllGamePackagesRecycleVersionsListByPage } from '/@/api/page/test'
import { useUserStoreWithOut } from '/@/store/modules/user'import { getAllGamePackagesRecycleVersionsListByPage } from '/@/api/page/test'
import { useUserStoreWithOut } from '/@/store/modules/user'
