import { PlatformEnterPoint } from './enterPoint';

const ForgeonTitleMap: Record<PlatformEnterPoint, string> = {
  // Modules
  [PlatformEnterPoint.ModuleHome]: '首页',
  [PlatformEnterPoint.ModuleDevelop]: '研发',
  [PlatformEnterPoint.ModuleTest]: '测试',
  [PlatformEnterPoint.ModuleManage]: '管理',
  [PlatformEnterPoint.ModuleAI]: 'AI',
  [PlatformEnterPoint.ModuleAccount]: '账号',
  [PlatformEnterPoint.ModuleEfficacy]: '统计',
  [PlatformEnterPoint.SysAdmin]: 'ForgeOn后台',
  [PlatformEnterPoint.SysAigc]: 'AI',
  [PlatformEnterPoint.DeptAsset]: '设备中心',
  [PlatformEnterPoint.SysForgeonAnalytics]: '统计平台',
  // 通用
  [PlatformEnterPoint.Redirect]: '',
  [PlatformEnterPoint.Error]: '',
  [PlatformEnterPoint.Forbidden]: 'Forbidden',
  [PlatformEnterPoint.NotFound]: 'NotFound',

  [PlatformEnterPoint.Login]: '登录',
  [PlatformEnterPoint.FeishuReturn]: '飞书登录返回页',
  [PlatformEnterPoint.LinkToApp]: 'APP跳转页',
  [PlatformEnterPoint.SSOReturn]: 'SSO登录返回页',
  [PlatformEnterPoint.LinkToOther]: '第三方SSO登录跳转页',
  [PlatformEnterPoint.Home]: '首页',
  [PlatformEnterPoint.DevGuard]: 'DevGuard',
  [PlatformEnterPoint.AccountSettings]: '个人设置',

  // 包体
  [PlatformEnterPoint.GamePackage]: '游戏包体中心',
  [PlatformEnterPoint.GamePackageCard]: '游戏包体中心包体检测卡片',
  [PlatformEnterPoint.GamePackageDoctor]: '游戏包体中心包体检测',
  [PlatformEnterPoint.GamePackageDoctorCompare]: '游戏包体中心包体检测对比',
  [PlatformEnterPoint.GamePackageDoctorProportion]: '游戏包体中心包体检测饼图',
  [PlatformEnterPoint.GamePackageDoctorTrend]: '游戏包体中心包体检测趋势图',
  [PlatformEnterPoint.GamePackageSettings]: '游戏包体中心配置',
  [PlatformEnterPoint.GamePackageVersionSimple]: '游戏包体中心包版本简要信息',
  // p4
  [PlatformEnterPoint.P4ClLabelManagement]: 'CL标签',
  [PlatformEnterPoint.P4CommitParamsConfigs]: '提交单号',
  [PlatformEnterPoint.P4CommitTagConfigs]: '提交tag',
  [PlatformEnterPoint.P4CompleteNoticeManagement]: '提交完成通知',
  [PlatformEnterPoint.P4CustomGroupManagement]: '自定义组',
  [PlatformEnterPoint.P4Depots]: 'DevGuard',
  [PlatformEnterPoint.P4FormDiff]: '表格Diff',
  [PlatformEnterPoint.P4FormDiffDetail]: '表格Diff详情',
  [PlatformEnterPoint.P4GroupManagement]: 'P4组配置',
  [PlatformEnterPoint.P4LdapGroupManagement]: 'ldap组',
  [PlatformEnterPoint.P4MemberManagement]: '项目成员配置',
  [PlatformEnterPoint.P4Onboarding]: '技术中心P4接触流程',
  [PlatformEnterPoint.P4OnboardingSettings]: '接触流程文档管理',
  [PlatformEnterPoint.P4OnboardingSettingsChild]: '接触流程文档管理子项目配置',
  [PlatformEnterPoint.P4Pass]: '技术中心P4通过成员统计页面',
  [PlatformEnterPoint.P4PermissionManagement]: '提交权限',
  [PlatformEnterPoint.P4Training]: 'P4学习',
  [PlatformEnterPoint.P4Trains]: '技术中心P4V操作手册',
  [PlatformEnterPoint.P4TrainsSettings]: 'P4V操作手册管理',
  [PlatformEnterPoint.P4Triggers]: 'P4 Triggers',
  [PlatformEnterPoint.P4TriggersOverview]: 'P4 Triggers总览',
  [PlatformEnterPoint.P4TriggersParamsSettings]: '参数配置',
  [PlatformEnterPoint.P4TriggersSettings]: 'P4 Triggers配置',
  // Oasis聚合
  [PlatformEnterPoint.Oasis]: 'Oasis配置',
  [PlatformEnterPoint.InstructionCombinations]: '指令集',
  [PlatformEnterPoint.ToolNavigations]: '常用网站',
  [PlatformEnterPoint.GroupChat]: '群聊配置',
  [PlatformEnterPoint.ToolGroupChat]: '工具反馈群配置',
  // 埋点平台
  [PlatformEnterPoint.Tracking]: '埋点',
  [PlatformEnterPoint.TrackingAnalysis]: '埋点分析平台',
  [PlatformEnterPoint.TrackingAnalysisSettings]: '配置',
  // 权限管理中心
  [PlatformEnterPoint.SysPermissionCenter]: '权限管理中心',
  [PlatformEnterPoint.PermissionCenterDashboard]: '权限管理中心',
  [PlatformEnterPoint.PermissionCenterApp]: '权限应用详情',
  [PlatformEnterPoint.PermissionCenterManagement]: '编辑权限接口',
  // 测试平台
  [PlatformEnterPoint.AssetLib]: '资源库',
  [PlatformEnterPoint.Automation]: 'UI自动化平台',
  [PlatformEnterPoint.AutomationReport]: '报告',
  [PlatformEnterPoint.AutomationSet]: '用例集配置',
  [PlatformEnterPoint.AutomationTask]: '任务配置',
  [PlatformEnterPoint.P4TouchProcess]: '技术中心P4接触流程【旧】',
  [PlatformEnterPoint.BugRobot]: 'BUG姬',
  [PlatformEnterPoint.BugRobotChats]: '群聊',
  [PlatformEnterPoint.CrashClassDetail]: 'Crash分类详情',
  [PlatformEnterPoint.CrashCollect]: 'Crash平台',
  [PlatformEnterPoint.CrashDetail]: 'Crash详情',
  [PlatformEnterPoint.DeptAssetApplyManagement]: '设备借用',
  [PlatformEnterPoint.CloudDevice]: '云真机',
  [PlatformEnterPoint.CloudDeviceDetail]: '云真机详情',
  [PlatformEnterPoint.DeptAssetsManagement]: '设备中心管理',
  [PlatformEnterPoint.DeptManagement]: '部门管理',
  [PlatformEnterPoint.DeptMemberManagement]: '干员管理',
  [PlatformEnterPoint.DeviceManagement]: '设备中心管理',
  [PlatformEnterPoint.DeviceManagementAdminConfig]: '管理员配置',
  [PlatformEnterPoint.DeviceManagementFaultList]: '报障列表',
  [PlatformEnterPoint.DeviceManagementLogs]: '使用记录',
  [PlatformEnterPoint.DM01GroupManagement]: 'DM01分组管理',
  [PlatformEnterPoint.GameArchive]: '游戏存档',
  [PlatformEnterPoint.Gitlab]: 'Git分支管理',
  [PlatformEnterPoint.GitlabReviewList]: '审查列表',
  [PlatformEnterPoint.GitlabReviewSettings]: '审查配置',
  [PlatformEnterPoint.GitlabSubmitDescriptionSpecifications]: '提交描述规范',
  [PlatformEnterPoint.HDALib]: 'HDA库',
  [PlatformEnterPoint.HomeSettings]: '首页配置',
  [PlatformEnterPoint.InstructionComponents]: '元件',
  [PlatformEnterPoint.Instructions]: '指令集',
  [PlatformEnterPoint.JenkinsAutoTask]: '光照烘焙任务',
  [PlatformEnterPoint.LoadTest]: '压测平台',
  [PlatformEnterPoint.MaterialLib]: '材质库',
  [PlatformEnterPoint.MenuManagement]: '菜单管理',
  [PlatformEnterPoint.MessageCenter]: '消息发送中心',
  [PlatformEnterPoint.OrganizationManagement]: '组织架构',
  [PlatformEnterPoint.OriginInstructions]: '原始指令集',
  [PlatformEnterPoint.OutsourcingPlatform]: '鹰角众包平台',
  [PlatformEnterPoint.PerfdeepCardCompare]: 'Unity性能卡片对比',
  [PlatformEnterPoint.PerfdeepCardTrend]: 'Unity性能卡片趋势',
  [PlatformEnterPoint.PerfdeepCase]: 'Unity性能',
  [PlatformEnterPoint.PerfdeepCaseDetail]: 'Unity性能报告',
  [PlatformEnterPoint.PerforceAccessLevelsSettings]: '权限类别配置',
  [PlatformEnterPoint.PerforceManagement]: 'P4权限配置',
  [PlatformEnterPoint.PerforceServersSettings]: '服务器配置',
  [PlatformEnterPoint.PerforceSettings]: 'P4权限配置',
  [PlatformEnterPoint.Performance]: '性能测试平台',
  [PlatformEnterPoint.PerformanceCard]: '性能卡片',
  [PlatformEnterPoint.PerformanceCardCompare]: 'Perfdog性能卡片对比',
  [PlatformEnterPoint.PerformanceCardTrend]: 'Perfdog性能卡片趋势',
  [PlatformEnterPoint.UnrealCase]: 'Unreal性能',
  [PlatformEnterPoint.UnrealCaseDetail]: 'Unreal性能报告',
  [PlatformEnterPoint.PerformanceCase]: 'Perfdog性能',
  [PlatformEnterPoint.PerformanceCaseCompare]: 'Perfdog性能对比',
  [PlatformEnterPoint.PerformanceCaseDetail]: 'Perfdog性能报告',
  [PlatformEnterPoint.PerformanceMap]: '性能地图',
  [PlatformEnterPoint.PerformanceReference]: '性能参照',
  [PlatformEnterPoint.PerformanceHeatMap]: '热力图详情',
  [PlatformEnterPoint.PerformanceHeatMapList]: '热力图',
  [PlatformEnterPoint.PerformanceHeatMapSettings]: '热力图设置',
  [PlatformEnterPoint.ProjectMember]: '项目干员管理',
  [PlatformEnterPoint.ProjectPermissionManagement]: '项目干员权限管理',
  [PlatformEnterPoint.ProjectsManagement]: '项目管理',
  [PlatformEnterPoint.ProtocolTest]: '协议测试平台',
  [PlatformEnterPoint.ProtocolTestDetail]: '协议测试详情页',
  [PlatformEnterPoint.ProtocolTestDevices]: '设备列表',
  [PlatformEnterPoint.ProtocolTestHistory]: '协议测试记录',
  [PlatformEnterPoint.ResourceCheck]: '提交检查',
  [PlatformEnterPoint.ResourceCheckIndexOld]: '资源检查规则【旧】',
  [PlatformEnterPoint.ResourceCheckItems]: '资源检查条目',
  [PlatformEnterPoint.ResourceCheckOld]: '提交检查配置【旧】',
  [PlatformEnterPoint.ResourceCheckReportCompare]: '检查报告对比',
  [PlatformEnterPoint.ResourceCheckReportDetail]: '检查报告详情',
  [PlatformEnterPoint.ResourceCheckReports]: '检查报告',
  [PlatformEnterPoint.ResourceCheckRules]: '文件命名检查',
  [PlatformEnterPoint.ResourceCheckRulesOld]: '命名规范【旧】',
  [PlatformEnterPoint.TCP4TSettings]: 'P4培训和考试管理',
  [PlatformEnterPoint.HomeSettingsChild]: '子功能配置',
  [PlatformEnterPoint.ResourceCheckSwitchesOld]: '功能开关【旧】',
  [PlatformEnterPoint.ResourceCheckSwitchesTemplate]: '功能开关模板',
  [PlatformEnterPoint.ResourceCheckTemplate]: '资源检查模板',
  [PlatformEnterPoint.RoleManage]: '角色管理',
  [PlatformEnterPoint.BannerManagement]: '首页 Banner',
  [PlatformEnterPoint.ProductionNews]: '产品动态',
  [PlatformEnterPoint.ProjectSettings]: '项目配置',
  [PlatformEnterPoint.Secure]: '游戏安全',
  [PlatformEnterPoint.SecureChannelsSettings]: '游戏安全配置',
  [PlatformEnterPoint.SecureProtections]: '防重签',
  [PlatformEnterPoint.SecureSettings]: '游戏安全配置',
  [PlatformEnterPoint.Services]: '测试平台',
  [PlatformEnterPoint.SwarmSettings]: '审查和关注',
  [PlatformEnterPoint.System]: '系统管理',
  [PlatformEnterPoint.SystemPerforceManagement]: '系统权限管理',
  [PlatformEnterPoint.TCP4TOperationsSettings]: '操作题',
  [PlatformEnterPoint.TCP4TSelectionsSettings]: '选择题',
  [PlatformEnterPoint.TCP4T]: '技术中心P4技能认证考试',
  [PlatformEnterPoint.Test]: '游戏体验',
  [PlatformEnterPoint.Tool]: '工具',
  [PlatformEnterPoint.ToolWorkPlatform]: '作业平台',
  [PlatformEnterPoint.Toolkit]: '工具中心',
  [PlatformEnterPoint.ToolkitDetail]: '工具详情',
  [PlatformEnterPoint.ToolkitPackageSettings]: '工具商店配置',
  [PlatformEnterPoint.WebHookPlatform]: '消息处理中心',
  [PlatformEnterPoint.MessageTemplate]: '消息模板',
  [PlatformEnterPoint.HomePageManagement]: '页面配置',
  // ai
  [PlatformEnterPoint.AIImage]: '图像能力',
  [PlatformEnterPoint.AIImagePage]: 'AI生图',
  [PlatformEnterPoint.ExtraImage]: 'AI图片超分',
  [PlatformEnterPoint.BgRemoval]: 'AI快速抠图',
  [PlatformEnterPoint.MyAssets]: '我的资产',

  [PlatformEnterPoint.Voice]: '语音能力',
  [PlatformEnterPoint.VoiceRecognition]: '语音识别',
  [PlatformEnterPoint.VoiceSynthesis]: '语音合成',
  [PlatformEnterPoint.VoiceAssets]: '语音资产',
  [PlatformEnterPoint.VoiceRemake]: '语音复刻',

  [PlatformEnterPoint.LLM]: '大模型能力',
  [PlatformEnterPoint.VersionNews]: '版本要闻',
  [PlatformEnterPoint.VersionNewsSecondSummary]: '版本报告',
  [PlatformEnterPoint.VersionNewsFirstSummary]: '版本要闻',
  [PlatformEnterPoint.VersionNewsOriginData]: '原始数据',
  [PlatformEnterPoint.AIChat]: 'AI对话助手',

  [PlatformEnterPoint.AITools]: '小工具',
  [PlatformEnterPoint.Watermask]: 'PDF/图片批量水印',
  [PlatformEnterPoint.IconCut]: 'ICON切图',
  [PlatformEnterPoint.ImageTo3D]: '图生3D',
  [PlatformEnterPoint.LineExtraction]: '线稿提取',
  [PlatformEnterPoint.LocalTranslate]: '本地化工具',
  [PlatformEnterPoint.Cutting]: '角色切图自动化',

  [PlatformEnterPoint.Conflux]: 'Conflux - 分支合并平台',
  [PlatformEnterPoint.ConfluxHistory]: '自动合并历史',
  [PlatformEnterPoint.ConfluxTask]: '待处理任务',

  [PlatformEnterPoint.AINotFound]: 'AINotFound',
  [PlatformEnterPoint.AIForbidden]: 'AIForbidden',

  [PlatformEnterPoint.EfficacyPreview]: '统计',
  [PlatformEnterPoint.Forgeflip]: 'P4服务器监控',

  [PlatformEnterPoint.CloudGame]: '云游戏',
};

export { ForgeonTitleMap };
