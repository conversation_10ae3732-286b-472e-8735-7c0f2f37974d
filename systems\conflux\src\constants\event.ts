/**
 * 定义事件名称的枚举, 事件名称的格式为: 模块名_事件名
 * @see https://dataark.hypergryph.net/area/11/app/app_manager/event_manager?appId=7l6k0y2vw4rksevp6bibwuht
 */
enum TrackEventName {
  /**
   * 创建规则
   */
  conflux_rule_create = 'conflux_rule_create',
  /**
   * 修改最新处理CL
   */
  conflux_latest_cl_modify = 'conflux_latest_cl_modify',
  /**
   * 在线处理保存进度
   */
  conflux_process_save = 'conflux_process_save',
  /**
   * 使用在线处理
   */
  conflux_online_resolve = 'conflux_online_resolve',
  /**
   * 使用本地处理
   */
  conflux_local_resolve = 'conflux_local_resolve',
  /**
   * 分配处理人
   */
  conflux_handler_assign = 'conflux_handler_assign',
  /**
   * 查看提交文件
   */
  conflux_go_to_swarm = 'conflux_go_to_swarm',
  /**
   * 重试合并失败任务
   */
  conflux_failed_merge_retry = 'conflux_failed_merge_retry',
  /**
   * 跳过合并失败任务
   */
  conflux_failed_merge_skip = 'conflux_failed_merge_skip',
  /**
   * 一键拉群
   */
  conflux_feishu_group_create = 'conflux_feishu_group_create',
  /**
   * 更新群聊
   */
  conflux_feishu_group_update = 'conflux_feishu_group_update',
}
interface ConfluxBaseParams {
  conflux_target_branch_path?: string;
  conflux_source_branch_path?: string;
  conflux_rule_id?: string;
}

// 定义每个事件的入参类型
type RuleCreateParams = ConfluxBaseParams & {
  conflux_trigger_type?: 'auto' | 'tag';
};

// 定义泛型类型来获取事件的入参类型
 type TrackEventParams<T extends TrackEventName> =
  T extends TrackEventName.conflux_rule_create ? RuleCreateParams :
    ConfluxBaseParams;

export {
  TrackEventName,
};
export type { TrackEventParams };
