<template>
  <PageWrapper :title="`可提交单号配置：${streamData?.description || streamData?.path}`" headerSticky @back="handleBack">
    <template #subTitle>
      <Button
        class="custom-rounded-btn" borderColor="black" noIcon size="small"
        :disabled="editingFieldsValue || editingConfigInfo || editingCommitTransition || editingKeyAttributes" @click="handleDelete"
      >
        删除
      </Button>
    </template>
    <template #extra>
      <template v-if="!initDataLoading && !isEqual(formValue, initFormValue)">
        <Button
          size="small" class="custom-rounded-btn" borderColor="black" noIcon
          :disabled="editingFieldsValue || editingConfigInfo || editingCommitTransition || editingKeyAttributes" @click="resetFormValue"
        >
          撤销
        </Button>
        <Button
          size="small" class="custom-rounded-btn" borderColor="primary" noIcon :disabled="initDataLoading"
          :loading="editingFieldsValue || editingConfigInfo || editingCommitTransition || editingKeyAttributes" @click="handleSubmit"
        >
          保存
        </Button>
      </template>
    </template>
    <div class="rounded-md bg-FO-Container-Fill1 p-4">
      <Spin :spinning="editingFieldsValue || editingConfigInfo || editingCommitTransition || editingKeyAttributes">
        <BorderBox label="强制绑定单号" subLabel="开启后使用提交工具提交提交必须绑定单号，否则无法通过提交工具提交。">
          <Spin :spinning="initDataLoading">
            <div class="ml">
              <span class="mr-[6px]">是否强制绑定单号</span>
              <Switch v-model:checked="formValue.enable" size="small" checkedChildren="开" unCheckedChildren="关" />
            </div>
            <div class="ml mt-8px">
              <span class="mr-[6px]">仅可绑定一个单号</span>
              <Switch v-model:checked="formValue.bindOneWorkItemIDLimit" size="small" checkedChildren="开" unCheckedChildren="关" />
            </div>
          </Spin>
        </BorderBox>
        <BorderBox label="需求单配置">
          <Spin :spinning="initDataLoading">
            <div>
              <div class="w-fit bg-FO-Container-Fill1 py-8px font-bold">
                需求单可提交单号
              </div>
              <div class="mb-8px font-size-12px c-FO-Content-Text2">
                配置需求单可提交单号属性后，用户使用提交工具时只可以使用符合条件的需求单提交。
              </div>
            </div>

            <Form ref="storyFormRef" name="StoryForm" :model="formValue.story" class="ml">
              <ParamsConfigFormItemsItems
                v-model:value="formValue.story" type="story" :fieldItems="storyFields"
                :roleOptions="storyRoleOptions"
              />
            </Form>
            <Divider />
            <div>
              <div class="w-fit bg-FO-Container-Fill1 py-8px font-bold">
                重要属性
              </div>
              <div class="mb-8px font-size-12px c-FO-Content-Text2">
                使用提交工具提交时，绑定单号后，在已绑定单号页面会高亮显示所绑定单号的重要值属性
              </div>
              <div class="flex items-center gap-[12px]">
                <div>重要属性:</div>
                <Select v-model:value="formValue.importantStory" :getPopupContainer="getPopupContainer" placeholder="请选择" style="width: 300px" mode="multiple" :options="storyFieldOptions" />
              </div>
            </div>
            <Divider />
            <div>
              <div class="w-fit bg-FO-Container-Fill1 py-8px font-bold">
                单号状态流转配置
              </div>
              <div class="mb-8px font-size-12px c-FO-Content-Text2">
                使用提交工具提交时，若选择单号时单号为指定状态，则提交完成后由提交中心自动更改至指定的提交后状态。
              </div>
            </div>
            <ParamsTransitionConfig
              v-model:value="formValue.transitionStory"
              class="ml" type="story" :error="storyError"
              :fieldNames="{ label: 'name', value: 'state_key' }"
              @storyChange="storyChange"
            />
          </Spin>
        </BorderBox>
        <BorderBox label="BUG单配置">
          <div>
            <div class="w-fit bg-FO-Container-Fill1 py-8px font-bold">
              BUG单可提交单号
            </div>
            <div class="mb-8px font-size-12px c-FO-Content-Text2">
              配置BUG单可提交单号属性后，用户使用提交工具时只可以使用符合条件的BUG单提交。
            </div>
          </div>
          <Spin :spinning="initDataLoading">
            <Form ref="issueFormRef" name="IssueForm" :model="formValue.issue" class="ml">
              <ParamsConfigFormItemsItems
                v-model:value="formValue.issue" type="issue" :fieldItems="issueFields"
                :roleOptions="issueRoleOptions"
              />
            </Form>
            <Divider />
            <div>
              <div class="w-fit bg-FO-Container-Fill1 py-8px font-bold">
                重要属性
              </div>
              <div class="mb-8px font-size-12px c-FO-Content-Text2">
                使用提交工具提交时，绑定单号后，在已绑定单号页面会高亮显示所绑定单号的重要值属性
              </div>
              <div class="flex items-center gap-[12px]">
                <div>重要属性:</div>
                <Select v-model:value="formValue.importantIssue" :getPopupContainer="getPopupContainer" placeholder="请选择" style="width: 300px" mode="multiple" :options="issueFieldOptions" />
              </div>
            </div>
            <Divider />
            <div>
              <div class="w-fit bg-FO-Container-Fill1 py-8px font-bold">
                单号状态流转配置
              </div>
              <div class="mb-8px font-size-12px c-FO-Content-Text2">
                使用提交工具提交时，若选择单号时单号为指定状态，则提交完成后由提交中心自动更改至指定的提交后状态。
              </div>
            </div>
            <ParamsTransitionConfig
              v-model:value="formValue.transitionIssue" type="issue" :error="issueError" class="ml"
              :fieldNames="{ label: 'name', value: 'state_key' }" @issueChange="issueChange"
            />
          </Spin>
        </BorderBox>
      </Spin>
      <DeleteModalHolder />
    </div>
  </PageWrapper>
</template>

<script setup lang="tsx">
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import { type FormInstance, Button, Divider, Form, message, Select, Spin, Switch } from 'ant-design-vue';
import { PageWrapper } from '../../../../components/Page/index.ts';
import type { CommitParamsItem, IssueItem, StoryItem, StreamsListItem } from '../../../../api/page/model/p4Model.ts';
import { useRouter } from 'vue-router';
import { BorderBox } from '../../../../components/Form/index.ts';
import { useLatestPromise, useModalShow } from '@hg-tech/utils-vue';
import DeleteModal from '../../../../components/DeleteModal.vue';
import {
  addCommitParams,
  deleteCommitParams,
  editCommitParams,
  editConfigInfo,
  getCommitParamsValue,
  getCommitTransitionValue,
  getConfigInfo,
  getKeyAttributes,
  setCommitTransitionValue,
  setKeyAttributes,
} from '../../../../api/page/p4.ts';
import { useUserStore } from '../../../../store/modules/user.ts';
import { computed, reactive, ref, watch } from 'vue';
import ParamsConfigFormItemsItems from './config/ParamsConfigFormItems.vue';
import ParamsTransitionConfig from './config/ParamsTransitionConfig.vue';
import { useParamsConfigInfo } from './config/useParamsConfig.ts';
import type { CommitParamsFormValue } from './config/helper.ts';
import { cloneDeep, isEqual } from 'lodash-es';
import { getPopupContainer } from '../../../../utils/index.ts';

const props = withDefaults(defineProps<{
  streamData: StreamsListItem;
  allStreamList?: StreamsListItem[];
}>(), {
  allStreamList: () => [],
});

const emit = defineEmits<{
  (e: 'update'): void;
}>();

const router = useRouter();
const userStore = useUserStore();
const [DeleteModalHolder, showDeleteModal] = useModalShow(DeleteModal);

const { data: configInfo, execute: fetchConfigInfo, loading: loadingConfigInfo } = useLatestPromise(getConfigInfo);
const { data: fieldsValue, execute: fetchFieldsValue, loading: loadingFieldsValue } = useLatestPromise(getCommitParamsValue);
const { data: transitionValue, execute: fetchTransitionValue, loading: loadingTransitionValue } = useLatestPromise(getCommitTransitionValue);
const { data: attributesValue, execute: fetchAttributesValue, loading: loadingAttributesValue } = useLatestPromise(getKeyAttributes);

const issueFormRef = ref<FormInstance>();
const {
  fieldInfo: issueFieldInfo,
  fieldItems: issueFields,
  initFormValue: issueInitFormValue,
  roleOptions: issueRoleOptions,
  fetchFields: fetchIssueFields,
  loading: loadingIssueFields,
  getSubmitData: getIssueSubmitData,
} = useParamsConfigInfo('issue', fieldsValue);
const storyFormRef = ref<FormInstance>();
const {
  fieldInfo: storyFieldInfo,
  fieldItems: storyFields,
  initFormValue: storyInitFormValue,
  roleOptions: storyRoleOptions,
  fetchFields: fetchStoryFields,
  loading: loadingStoryFields,
  getSubmitData: getStorySubmitData,
} = useParamsConfigInfo('story', fieldsValue);
const initDataLoading = computed(() => loadingConfigInfo.value || loadingAttributesValue.value || loadingIssueFields.value || loadingStoryFields.value || loadingFieldsValue.value || loadingTransitionValue.value);
const { execute: updateConfigInfo, loading: editingConfigInfo } = useLatestPromise(editConfigInfo);
const { execute: editKeyAttributes, loading: editingKeyAttributes } = useLatestPromise(setKeyAttributes);
const { execute: editFieldsValue, loading: editingFieldsValue } = useLatestPromise((projectID: number, data: CommitParamsItem, editId?: number) => {
  if (editId) {
    return editCommitParams(projectID, data, editId);
  }
  return addCommitParams(projectID, data);
});
const { execute: editCommitTransition, loading: editingCommitTransition } = useLatestPromise(setCommitTransitionValue);

const formValue = reactive({
  enable: false,
  bindOneWorkItemIDLimit: false,
  importantStory: [] as string[],
  importantIssue: [] as string[],
  story: {} as CommitParamsFormValue,
  issue: {} as CommitParamsFormValue,
  transitionStory: [] as StoryItem[],
  transitionIssue: [] as IssueItem[],
});

watch([() => userStore.getProjectId, () => props.streamData.ID], ([projectId, streamId], [oldProjectId, oldStreamId]) => {
  if (projectId && streamId && (projectId !== oldProjectId || streamId !== oldStreamId)) {
    refreshFields(projectId, streamId);
  }
}, { immediate: true });

const storyFieldOptions = computed(() =>

  storyFields.value.filter((item) => ['multi_select', 'select'].includes(item.field_type_key || '')).map((item) => ({
    value: item.field_key,
    label: item.field_name,
  })),
);
const issueFieldOptions = computed(() =>
  issueFields.value.filter((item) => ['multi_select', 'select'].includes(item.field_type_key || '')).map((item) => ({
    value: item.field_key,
    label: item.field_name,
  })),
);

async function refreshFields(projectId: number, streamId: number) {
  await Promise.all([
    fetchConfigInfo(projectId, streamId),
    fetchIssueFields(projectId),
    fetchStoryFields(projectId),
    fetchFieldsValue(projectId, streamId),
    fetchTransitionValue(projectId, streamId),
    fetchAttributesValue(projectId, streamId),
  ]);
  resetFormValue();
}

const initFormValue = computed(() => ({
  enable: configInfo.value?.enable != null ? configInfo.value.enable > 0 : false,
  bindOneWorkItemIDLimit: configInfo.value?.bindOneWorkItemIDLimit != null ? configInfo.value.bindOneWorkItemIDLimit > 0 : false,
  issue: { ...issueInitFormValue.value },
  story: { ...storyInitFormValue.value },
  transitionStory: transitionValue.value?.story,
  transitionIssue: transitionValue.value?.issue,
  importantStory: attributesValue.value?.list?.story?.map((e) => e.fieldKey) ?? [],
  importantIssue: attributesValue.value?.list?.issue?.map((e) => e.fieldKey) ?? [],
}));

function resetFormValue() {
  issueFormRef.value?.resetFields();
  storyFormRef.value?.resetFields();
  formValue.enable = initFormValue.value.enable;
  formValue.bindOneWorkItemIDLimit = initFormValue.value.bindOneWorkItemIDLimit;
  formValue.transitionStory = cloneDeep(initFormValue.value.transitionStory ?? []);
  formValue.transitionIssue = cloneDeep(initFormValue.value.transitionIssue ?? []);
  formValue.issue = cloneDeep(initFormValue.value.issue);
  formValue.story = cloneDeep(initFormValue.value.story);
  formValue.importantStory = cloneDeep(initFormValue.value.importantStory);
  formValue.importantIssue = cloneDeep(initFormValue.value.importantIssue);
}

function handleBack() {
  router.push({ name: PlatformEnterPoint.P4Depots });
}
async function handleDelete() {
  await showDeleteModal({
    title: '删除可提交单号配置',
    description: (
      <div>
        <div>即将删除: </div>
        <div>
          <div>
            <span>该分支下的</span>
            <b>可提交单号配置</b>
          </div>
          <div>
            这会使
            <b>提交人获取项目全部单号</b>
          </div>
          <div>删除后，您可以随时重新配置</div>
        </div>
      </div>
    ),
    async onOk() {
      if (props.streamData.ID && userStore.getProjectId) {
        return deleteCommitParams(userStore.getProjectId, props.streamData.ID);
      }
    },
  });
  emit('update');
}
const storyError = ref<string[]>([]);
const issueError = ref<string[]>([]);
function storyValueValidate(transitionValue: StoryItem[]) {
  storyError.value = [];
  let isError = false;
  transitionValue.forEach((item, index) => {
    if (!item.states || !item.states?.length) {
      isError = true;
      storyError.value.push('未配置完整');
      document.getElementById(`story-item-${index}`)?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    } else {
      storyError.value.push('');
    }
  });
  if (isError) {
    throw new Error('未配置完整');
  }
}
function issueValueValidate(transitionValue: IssueItem[]) {
  issueError.value = [];
  let isError = false;
  transitionValue.forEach((item, index) => {
    if (!item.toState || !item.toState?.length || !item.fromStates || !item.fromStates?.length) {
      issueError.value.push('未配置完整');
      isError = true;
      document.getElementById(`issue-item-${index}`)?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    } else if (item.fromStates.includes(item.toState)) {
      issueError.value.push('提交前单号状态配置重复');
      isError = true;
      document.getElementById(`issue-item-${index}`)?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    } else {
      issueError.value.push('');
    }
  });
  if (isError) {
    throw new Error('bug单自动流转编辑错误');
  }
}

function storyChange() {
  storyValueValidate(formValue.transitionStory.slice(0, -1));
}

function issueChange() {
  issueValueValidate(formValue.transitionIssue.slice(0, -1));
}

function getImportantSubmitData(type: 'story' | 'issue', value: string[]) {
  if (type === 'story') {
    return {
      streamID: props.streamData.ID,
      workItemTypeKey: 'story' as const,
      attributes: value.map((e) => ({ fieldKey: e, fieldName: storyFields.value.find((f) => f.field_key === e)?.field_name ?? '' })),
    };
  }
  return {
    streamID: props.streamData.ID,
    workItemTypeKey: 'issue' as const,
    attributes: value.map((e) => ({ fieldKey: e, fieldName: issueFields.value.find((f) => f.field_key === e)?.field_name ?? '' })),
  };
}

async function handleSubmit() {
  if (!props.streamData.ID) {
    return;
  }
  await Promise.all([
    issueFormRef.value?.validate(),
    storyFormRef.value?.validate(),
    storyValueValidate(formValue.transitionStory),
    issueValueValidate(formValue.transitionIssue),
  ]);
  let res = null;
  if (userStore.getProjectId) {
    res = await Promise.all([
      updateConfigInfo(userStore.getProjectId, props.streamData.ID, formValue.enable ? 1 : 0, formValue.bindOneWorkItemIDLimit ? 1 : 0),
      editFieldsValue(userStore.getProjectId, getIssueSubmitData(props.streamData.ID, formValue.issue), issueFieldInfo.value?.ID),
      editFieldsValue(userStore.getProjectId, getStorySubmitData(props.streamData.ID, formValue.story), storyFieldInfo.value?.ID),
      editCommitTransition(userStore.getProjectId, props.streamData.ID, formValue.transitionStory, formValue.transitionIssue),
      editKeyAttributes(userStore.getProjectId, getImportantSubmitData('story', formValue.importantStory)),
      editKeyAttributes(userStore.getProjectId, getImportantSubmitData('issue', formValue.importantIssue)),
    ]);
  }
  if (res && res.every((r) => r?.code !== 7)) {
    message.success('保存成功');
    if (userStore.getProjectId && props.streamData.ID) {
      refreshFields(userStore.getProjectId, props.streamData.ID);
    }
  }
}
</script>
