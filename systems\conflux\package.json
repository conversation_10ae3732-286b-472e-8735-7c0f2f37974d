{"name": "@hg-tech/conflux", "type": "module", "version": "1.0.1", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "build:rnd": "cross-env NODE_ENV=production vite build --mode rnd", "build:pre": "cross-env NODE_ENV=production vite build --mode pre", "build:analyze": "vite build -- --analyze", "test": "run-p test:*", "test:type": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "dependencies": {"@ant-design/icons-vue": "catalog:", "@antv/x6": "catalog:", "@antv/x6-vue-shape": "catalog:", "@hg-tech/api-schema-merge": "^1.0.51", "@hg-tech/forgeon-style": "workspace:*", "@hg-tech/forgeon-uno-config": "workspace:^", "@hg-tech/oasis-common": "workspace:*", "@hg-tech/request-api": "workspace:*", "@hg-tech/utils": "workspace:^", "@hg-tech/utils-vue": "workspace:*", "@micro-zoe/micro-app": "catalog:", "@vueuse/core": "catalog:", "@vueuse/integrations": "catalog:", "@vueuse/router": "catalog:", "ant-design-vue": "catalog:", "dayjs": "catalog:", "lodash": "catalog:", "pinia": "catalog:", "pinyin-pro": "catalog:", "sortablejs": "catalog:", "unocss": "catalog:", "vue": "catalog:", "vue-router": "catalog:", "vxe-pc-ui": "catalog:", "vxe-table": "catalog:"}, "devDependencies": {"@hg-tech/configs": "workspace:^", "@types/lodash": "catalog:", "@types/sortablejs": "catalog:", "cross-env": "catalog:", "vite": "catalog:", "vite-svg-loader": "catalog:", "vue-tsc": "catalog:"}}