<template>
  <BasicDrawer
    showFooter
    title="添加版本"
    width="680px"
    @register="registerDrawer"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm">
      <template #attachments="{ model, field }">
        <div :class="`${prefixCls}__att-content`">
          <AFormItemRest>
            <template v-for="att in model[field]" :key="att.ID">
              <div :class="`${prefixCls}__att-item`">
                <Input
                  v-model:value="att.name"
                  :bordered="false"
                  class="!w-100px !px-1"
                  @blur="handleAttEdit(att)"
                />

                <Icon icon="ant-design:edit-outlined" />

                <Popconfirm
                  title="确定删除该资源吗?"
                  overlayClassName="!w-190px"
                  @confirm="handleAttDelete(att)"
                >
                  <a-button
                    preIcon="ant-design:minus-outlined"
                    :class="`${prefixCls}__att-del-btn`"
                    shape="circle"
                    size="small"
                    @click.stop
                  />
                </Popconfirm>
              </div>
            </template>
          </AFormItemRest>
          <BasicUpload
            uploadType="appstore"
            valueFormat="string"
            :maxNumber="1"
            :multiple="false"
            noAlert
            :showUploadList="false"
            @change="handleAttAdd"
            @isLoading="handleAttLoading"
          >
            <template #default="{ loading, stateMsg }">
              <div class="flex items-center">
                <a-button
                  preIcon="ant-design:plus-outlined"
                  :class="`${prefixCls}__btn !px-4px`"
                  size="small"
                  shape="circle"
                  :disabled="loading"
                />
                <div v-if="stateMsg" class="inline-block pl-3 c-FO-Functional-Warning1-Default">
                  {{ stateMsg }}
                  <span :class="`${uploadPrefixCls}__loading-dot`">...</span>
                </div>
              </div>
            </template>
          </BasicUpload>
        </div>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { Form, Input, Popconfirm } from 'ant-design-vue';
import { isEmpty, isEqual, omit } from 'lodash-es';
import { ref, unref } from 'vue';
import { formSchema } from './version.data';
import { type GameAttachmentsListItem, GamePackageCenterTrackOperation, GamePackageCenterTrackTrigger } from '/@/api/page/model/testModel';
import {
  addGameAttachment,
  addGamePackagesVersion,
  editGamePackagesVersionFinish,
  getGamePackagesVersionsListByPage,
} from '/@/api/page/test';
import { type DrawerInstance, BasicDrawer, useDrawerInner } from '/@/components/Drawer';
import { BasicForm, useForm } from '/@/components/Form/index';
import { Icon } from '/@/components/Icon';
import { BasicUpload } from '/@/components/Upload';
import { useTrack } from '/@/hooks/system/useTrack';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMessage } from '/@/hooks/web/useMessage';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { platformOptions } from '/@/views/test/gamePackage/settings/settings.data';
import { DevicePlatform } from '/@/views/toolkit/settings/toolkitSettings.data';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import { usePermissionCheckPoint } from '/@/service/permission/usePermission';

const emit = defineEmits<{
  (e: 'success', arg: 'add' | 'edit'): void;
  (e: 'register', modalInstance: DrawerInstance, uuid: number): void;
}>();

const AFormItemRest = Form.ItemRest;
const { prefixCls } = useDesign('game-package-settings-version-drawer');
const { prefixCls: uploadPrefixCls } = useDesign('single-upload');
const userStore = useUserStoreWithOut();
const editId = ref();
const pkgID = ref();
const recordData = ref();

const platforms = ref<number[]>([]);
const lastVersion = ref<string>('');
const { createWarningModal, createMessage } = useMessage();
const attErrorMsg = ref<string>('');
const isOasis = ref<boolean>(false);
const isMtl = ref<boolean>(false);
const showCloudGame = ref<boolean>(false);
const [hasCloudGameDeployPermission] = usePermissionCheckPoint({
  scope: PlatformEnterPoint.GamePackage,
  any: ['cloud_game_deployment'],
});
const [
  registerForm,
  { resetFields, setFieldsValue, validate, updateSchema, getFieldsValue },
] = useForm({
  labelWidth: 100,
  schemas: formSchema,
  showActionButtonGroup: false,
  baseColProps: { span: 23 },
});

async function getLastVersion() {
  if (!pkgID.value || !userStore.getProjectId) {
    return;
  }
  const { list } = await getGamePackagesVersionsListByPage(
    userStore.getProjectId,
    pkgID.value,
    {
      page: 1,
      pageSize: 1,
    },
  );
  lastVersion.value = list?.[0]?.version || '';
}

const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
  await resetFields();
  setDrawerProps({ confirmLoading: false });
  recordData.value = data?.record;
  editId.value = data?.record?.ID;
  pkgID.value = data?.pkgID;
  platforms.value = data?.platforms || [];
  isOasis.value = !!data?.isOasis;
  isMtl.value = !!data?.isMtl;
  showCloudGame.value = !!data?.showCloudGame;
  await getLastVersion();
  const showPlatformOptions = platformOptions.filter((e) =>
    platforms.value.includes(e.value!),
  );
  await updateSchema([
    {
      field: 'version',
      itemProps: {
        extra: lastVersion.value ? `*上一个版本号为：${lastVersion.value}` : undefined,
      },
    },
    // 表单平台选择仅可选择工具支持的
    {
      field: 'platform',
      componentProps: {
        options: showPlatformOptions,
      },
    },
    // 上传文件同步改变抽屉加载状态
    {
      field: 'pkgFile',
      componentProps: {
        onIsLoading: (val: boolean) => {
          setDrawerProps({ confirmLoading: val });
        },
        onGetFileSize: (size: number) => {
          setFieldsValue({
            sizeKB: size,
          });
        },
        // 根据文件后缀自动选择平台
        onChange: (val: string) => {
          const suffix = val.substring(val.lastIndexOf('.') + 1);
          let curPlatform = 3;
          switch (suffix) {
            // android
            case 'apk':
              curPlatform = 1;
              break;
              // ios
            case 'ipa':
              curPlatform = 2;
              break;
              // mac
            case 'dmg':
              curPlatform = 4;
              break;
              // playstation
            case 'pkg':
              curPlatform = 6;
              break;
              // // switch
              // case 'nsp':
              //   curPlatform = 7;
              //   break;
              // // xbox
              // case 'xvc':
              //   curPlatform = 8;
              // default:
              //   break;
          }
          if (!showPlatformOptions.find((e) => e.value === curPlatform)) {
            createWarningModal({
              title: '请注意',
              content:
                  '上传文件不在当前体验包支持范围，请先修改体验包平台范围！（若判断有误，请忽视该警告）',
            });
          } else if (!getFieldsValue().platform) {
            setFieldsValue({
              platform: curPlatform,
            });
          }
        },
      },

    },
    {
      field: 'CloudGameSetting',
      ifShow: ({ values }) => {
        return showCloudGame.value && hasCloudGameDeployPermission.value && values?.platform === DevicePlatform.Windows;
      },
    },
  ]);
});

// 添加附件
async function handleAttAdd(url: string) {
  if (!url) {
    return;
  }
  const att: GameAttachmentsListItem = {
    name: url.substring(url.lastIndexOf('/') + 1),
    downloadLink: url,
  };
  await setFieldsValue({
    attachments: [...(getFieldsValue().attachments || []), att],
  });
}

// 编辑附件
async function handleAttEdit(att: GameAttachmentsListItem) {
  if (isEmpty(att.name?.trim())) {
    attErrorMsg.value = '资源名不能为空';
    createMessage.warning(attErrorMsg.value);
  } else if (
    getFieldsValue().attachments?.some((e: GameAttachmentsListItem) => e.name === att.name && !isEqual(e, att))
  ) {
    attErrorMsg.value = '资源名不能重复';
    createMessage.warning(attErrorMsg.value);
  } else {
    attErrorMsg.value = '';
  }
}

async function handleAttDelete(att: GameAttachmentsListItem) {
  await setFieldsValue({
    attachments: getFieldsValue().attachments?.filter((e: GameAttachmentsListItem) => !isEqual(e, att)),
  });
}

function handleAttLoading(loading: boolean) {
  setDrawerProps({ confirmLoading: loading });
}

async function handleSubmit() {
  if (!userStore.getProjectId) {
    return;
  }
  if (attErrorMsg.value) {
    createMessage.warning(attErrorMsg.value);
    return;
  }
  try {
    const values = await validate();
    setDrawerProps({ confirmLoading: true });
    const { id } = await addGamePackagesVersion(userStore.getProjectId, unref(pkgID), {
      ...omit(values, 'deploy'),
      attachments: undefined,
      unfinished: !!values.attachments?.length,
    }, {
      deploy: values.deploy,
      trigger: isOasis.value ? GamePackageCenterTrackTrigger.Oasis : GamePackageCenterTrackTrigger.Web,
      operation: isMtl.value ? GamePackageCenterTrackOperation.CloudGame : (isOasis.value ? GamePackageCenterTrackOperation.Oasis : GamePackageCenterTrackOperation.Page),
    });
    if (!id) {
      return;
    }
    if (values.attachments?.length) {
      await Promise.all(
        values.attachments.map((e: GameAttachmentsListItem) => {
          if (!userStore.getProjectId) {
            return undefined;
          }
          return addGameAttachment(userStore.getProjectId, { ...e, versionID: id });
        },
        ),
      );
      await editGamePackagesVersionFinish(userStore.getProjectId, pkgID.value, id);
    }
    const { setTrack } = useTrack();
    setTrack('gw5exdf0yk');
    emit('success', 'add');

    closeDrawer();
  } finally {
    setDrawerProps({ confirmLoading: false });
  }
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-game-package-settings-version-drawer';

.@{prefix-cls} {
  &__att-del-btn,
  &__btn {
    border-color: #4d4d4d !important;
    background-color: #4d4d4d !important;
    color: #fff !important;

    &-pro {
      border-color: #f8cc28 !important;
      background-color: #f8cc28 !important;
      color: #000 !important;
      font-weight: bold;
    }
  }

  &__att {
    &-content {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      row-gap: 8px;
    }

    &-item {
      display: flex;
      position: relative;
      align-items: center;
      margin-right: 16px;
      padding: 2px 10px;
      border-radius: 100px;
      background-color: @FO-Container-Fill5;
      font-size: 12px;
    }

    &-del-btn {
      display: flex;
      position: absolute !important;
      top: -6px;
      right: -6px;
      align-items: center;
      justify-content: center;
      min-width: 8px !important;
      height: 16px !important;
      font-size: 6px !important;
    }
  }
}
</style>
