import { fontCssVarName, ForgeonThemeCssVar } from '@hg-tech/forgeon-style';

/**
 * key from 'vxe-table/styles/theme';
 */
export const cssVars = {
  /* font color */
  '--vxe-ui-font-color': ForgeonThemeCssVar.ContentText1,
  '--vxe-ui-font-primary-color': ForgeonThemeCssVar.BrandPrimaryDefault,
  '--vxe-ui-font-disabled-color': ForgeonThemeCssVar.ContentText3,
  '--vxe-ui-font-lighten-color': '#797b80',
  '--vxe-ui-font-darken-color': '#47494c',

  /* font family */
  '--vxe-ui-font-family': '-apple-system,BlinkMacSystemFont,Segoe UI,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol',

  /* font size */
  '--vxe-ui-font-size-default': fontCssVarName.FontR14FontSize,
  '--vxe-ui-font-size-medium': fontCssVarName.FontR14FontSize,
  '--vxe-ui-font-size-small': fontCssVarName.FontR12FontSize,
  '--vxe-ui-font-size-mini': fontCssVarName.FontR12FontSize,

  /* base */
  '--vxe-ui-border-radius': '4px',
  '--vxe-ui-base-popup-border-color': '#DADCE0',
  '--vxe-ui-base-popup-box-shadow': '0 0 10px 0 rgba(0, 0, 0, 0.16)',

  /* layout */
  '--vxe-ui-layout-background-color': 'transparent',

  /* input */
  '--vxe-ui-input-border-color': ForgeonThemeCssVar.ContainerStroke3,
  '--vxe-ui-input-placeholder-color': '#C0C4CC',
  '--vxe-ui-input-disabled-background-color': '#f3f3f3',
  '--vxe-ui-input-disabled-color': '#dcdfe6',
  '--vxe-ui-input-height-default': '34px',
  '--vxe-ui-input-height-medium': '32px',
  '--vxe-ui-input-height-small': '30px',
  '--vxe-ui-input-height-mini': '28px',

  /* loading */
  '--vxe-ui-loading-color': ForgeonThemeCssVar.BrandPrimaryDefault,
  '--vxe-ui-loading-background-color': ForgeonThemeCssVar.ContainerFill1,

  /* table */
  '--vxe-ui-table-header-font-color': ForgeonThemeCssVar.ContentText2,
  '--vxe-ui-table-header-background-color': 'transparent',
  '--vxe-ui-table-footer-font-color': ForgeonThemeCssVar.ContentText2,
  '--vxe-ui-table-footer-background-color': 'transparent',
  '--vxe-ui-table-border-radius': '4px',
  '--vxe-ui-table-border-width': '1.05px',
  '--vxe-ui-table-border-color': ForgeonThemeCssVar.ContainerStroke1,
  '--vxe-ui-table-resizable-line-color': ForgeonThemeCssVar.ContainerStroke2,
  '--vxe-ui-table-resizable-drag-line-color': ForgeonThemeCssVar.BrandPrimaryDefault,
  '--vxe-ui-table-column-to-row-background-color': '#f8f8f9',
  '--vxe-ui-table-tree-node-line-color': '#909399',
  '--vxe-ui-table-tree-node-line-style': 'dotted',
  '--vxe-ui-table-header-font-weight': '400',

  '--vxe-ui-table-row-height-default': '48px',
  '--vxe-ui-table-row-height-medium': '44px',
  '--vxe-ui-table-row-height-small': '40px',
  '--vxe-ui-table-row-height-mini': '36px',
  '--vxe-ui-table-row-line-height': '22px',
  '--vxe-ui-table-row-hover-background-color': ForgeonThemeCssVar.ContainerFill2,
  '--vxe-ui-table-row-striped-background-color': ForgeonThemeCssVar.ContainerFill3,
  '--vxe-ui-table-row-hover-striped-background-color': ForgeonThemeCssVar.ContainerFill3,
  '--vxe-ui-table-row-radio-checked-background-color': ForgeonThemeCssVar.ContainerFill4,
  '--vxe-ui-table-row-hover-radio-checked-background-color': ForgeonThemeCssVar.BrandTertiaryActive,
  '--vxe-ui-table-row-checkbox-checked-background-color': ForgeonThemeCssVar.BrandTertiaryActive,
  '--vxe-ui-table-row-hover-checkbox-checked-background-color': ForgeonThemeCssVar.BrandTertiaryActive,
  '--vxe-ui-table-row-current-background-color': ForgeonThemeCssVar.BrandTertiaryActive,
  '--vxe-ui-table-row-hover-current-background-color': ForgeonThemeCssVar.ContainerFill3,

  '--vxe-ui-table-column-hover-background-color': ForgeonThemeCssVar.ContainerFill3,
  '--vxe-ui-table-column-current-background-color': ForgeonThemeCssVar.BrandTertiaryActive,
  '--vxe-ui-table-column-icon-border-color': '#c0c4cc',
  '--vxe-ui-table-column-icon-border-hover-color': '#515A6E',

  '--vxe-ui-table-cell-padding-default': '10px',
  '--vxe-ui-table-cell-padding-medium': '8px',
  '--vxe-ui-table-cell-padding-small': '6px',
  '--vxe-ui-table-cell-padding-mini': '4px',
  '--vxe-ui-table-cell-placeholder-color': '#C0C4CC',
  '--vxe-ui-table-cell-negative-color': '#f56c6c',
  '--vxe-ui-table-cell-dirty-width': '5px',
  '--vxe-ui-table-cell-dirty-update-color': '#f56c6c',
  '--vxe-ui-table-cell-dirty-insert-color': '#19A15F',
  '--vxe-ui-table-cell-area-border-color': '#409eff',
  '--vxe-ui-table-cell-area-border-width': '1px',
  '--vxe-ui-table-cell-area-background-color': 'rgba(64,158,255,0.2)',
  '--vxe-ui-table-header-active-area-background-color': 'rgba(64,158,255,0.05)',
  '--vxe-ui-table-fixed-scrolling-box-shadow-color': 'rgba(0, 0, 0, 0.12)',
  '--vxe-ui-table-drag-over-background-color': 'rgba(255,255,200,0.3)',

  /* toolbar */
  '--vxe-ui-toolbar-custom-active-background-color': '#D9DADB',
};
