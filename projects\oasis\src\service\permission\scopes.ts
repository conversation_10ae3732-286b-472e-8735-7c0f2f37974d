import { type PermissionInfo, PlatformEnterPoint } from '@hg-tech/oasis-common';
import { getAllProjectList } from '/@/hooks/useProjects.ts';

const scopePermissionCodes: Partial<Record<PlatformEnterPoint, string>> = {
  [PlatformEnterPoint.SysAdmin]: 'admin',
  [PlatformEnterPoint.SysAigc]: 'tech_aigc',
  [PlatformEnterPoint.DeptAsset]: 'deptAsset',
  [PlatformEnterPoint.Conflux]: 'Conflux',
  [PlatformEnterPoint.GamePackage]: 'game_store',
  [PlatformEnterPoint.SysForgeonAnalytics]: 'P4Dashboard',
  [PlatformEnterPoint.CloudGame]: 'cloudGame',
};
const codeToPermissionPoint = Object.fromEntries(Object.entries(scopePermissionCodes).map(([key, value]) => [value, key]));

export async function getScopePermissions(projectId?: number): Promise<PermissionInfo['scopes']> {
  const data = await getAllProjectList();
  const projectInfo = data?.find((item) => item.ID === projectId);
  const { getAppPermissions } = await import('../../api/permissionCenter.ts');
  const permissionRes = await getAppPermissions({}, {
    projectId: projectInfo?.ID?.toString(),
    appCodes: Object.values(scopePermissionCodes),
  });
  return Object.entries(permissionRes?.data?.data?.appPermissions || {}).reduce((acc, [appCode, permissions]) => {
    const permissionPoint = codeToPermissionPoint[appCode];
    if (permissionPoint) {
      acc[permissionPoint] = permissions;
    }
    return acc;
  }, {} as PermissionInfo['scopes']);
}
