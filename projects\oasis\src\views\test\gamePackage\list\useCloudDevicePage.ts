import { PlatformEnterPoint, preprocessFilePath } from '@hg-tech/oasis-common';
import { useUserStore } from '../../../../store/modules/user.ts';
import { useRouter } from 'vue-router';
import { type MaybeRef, unref } from 'vue';
import type { GamePackageCenterTrackSourceType, GamePackagesListItem, GamePackagesVersionsListItem } from '../../../../api/page/model/testModel.ts';
import { sendEvent } from '/@/service/tracker/index.ts';
import { GamePackageCenterTrackTrigger } from '../../../../api/page/model/testModel.ts';

export function useCloudPhonePage(isOasis: MaybeRef) {
  const userStore = useUserStore();
  const router = useRouter();

  function openCloudPhonePage(pkgId: GamePackagesListItem['ID'], pkgVersionId: GamePackagesVersionsListItem['ID'], sourceType: GamePackageCenterTrackSourceType) {
    sendEvent('game_package_center_redirect_cloud', {
      game_package_center_package_id: pkgVersionId,
      game_package_center_branch_id: pkgId,
      game_package_center_source_type: sourceType,
      game_package_center_trigger_channel: unref(isOasis) ? GamePackageCenterTrackTrigger.Oasis : GamePackageCenterTrackTrigger.Web,

    });
    const cloudDeviceLink = router.resolve({
      name: PlatformEnterPoint.CloudDevice,
      query: {
        p: userStore.getProjectId,
        pkgId,
        pkgVersionId,
      },
    }).href;
    if (unref(isOasis)) {
      window.open(`oasisdownload://open-browser?url=${encodeURIComponent(preprocessFilePath(cloudDeviceLink))}`, '_blank');
    } else {
      window.open(cloudDeviceLink, '_blank');
    }
  }

  return {
    openCloudPhonePage,
  };
}
