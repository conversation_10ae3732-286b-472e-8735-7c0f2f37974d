import type {
  AccessLevelList,
  ApplyLogListGetResultModel,
  ApplyLogListItem,
  ApplyLogListPageParams,
  BatchRecycleDeviceParams,
  BatchUpdateDeviceFieldResult,
  BrandsListGetResultModel,
  BrandsListItem,
  BrandsListPageParams,
  ChangeReturnTimeParams,
  CheckDeviceResultModel,
  ChipsetsListGetResultModel,
  ChipsetsListItem,
  ChipsetsListPageParams,
  DeviceAdminDeviceListParams,
  DeviceAdminListGetResultModel,
  DeviceAdminListItem,
  DeviceCategoryTypeEnum,
  DeviceListGetResultModel,
  DeviceListItem,
  DeviceListItemParams,
  DeviceListPageParams,
  DeviceOptLogListGetResultModel,
  DeviceOptLogListPageParams,
  DeviceTagGetResultModel,
  FaultListGetResultModel,
  FaultListItem,
  FaultTypeListGetResultModel,
  FilterTemplateListItem,
  getDeviceFilterTemplateListResultModel,
  OverseasChipsetsListGetResultModel,
  OverseasChipsetsListPageParams,
  OverseasDevBrandListGetResultModel,
  OverseasDevListGetResultModel,
  OverseasDevListPageParams,
  UpdateDeviceFieldParams,
  UsageListGetResultModel,
  UsageLogListGetResultModel,
  UsageLogListItem,
  UsageLogListPageParams,
} from './model/deptAssetModel';
import type { BasicAddResult, BasicFetchResult, BasicPageParams, BasicResult, NullableBasicResult } from '/@/api/model/baseModel';
import { defHttp } from '/@/utils/http/axios';
import qs from 'qs';
/**
 * 获取设备列表
 * @param params
 */
export function getDeviceList(params?: DeviceListPageParams) {
  return defHttp.get<DeviceListGetResultModel>({ url: '/api/v1/asset_devices', params });
}

/**
 * 根据id获取设备信息
 * @param ID 设备id
 */
export function getDeviceByID(ID: number) {
  return defHttp.get<DeviceListItemParams>({ url: `/api/v1/asset_devices/${ID}` });
}

/**
 * 创建设备
 * @param data 设备数据
 */
export const addDevice = (data: DeviceListItem) => defHttp.post<BasicAddResult>({ url: '/api/v1/asset_devices', data });

/**
 * 编辑设备
 * @param data 设备数据
 * @param editId 设备id
 */
export function editDevice(data: DeviceListItem, editId: number) {
  return defHttp.put<NullableBasicResult>({ url: `/api/v1/asset_devices/${editId}`, data });
}
/**
 * 更新设备部分字段
 * @param editId 设备id
 * @param data 设备数据
 * @param data.remark 备注
 * @param data.accessLevel 设备流通级别
 * @param data.deptIds 设备所属部门id列表
 * @param data.projectIds 设备所属项目id列表
 */
export function editPartialDevice(editId: number, data: { remark?: string; accessLevel?: number; deptIds?: number[]; projectIds?: number[] }) {
  return defHttp.put<NullableBasicResult>({ url: `/api/v1/asset_devices/${editId}/partial`, data });
}

/**
 * 批量更新设备流通级别
 * @param data 设备数据
 */
export function batchUpdateDevice(data: DeviceListItem & { ids: number[] }) {
  return defHttp.put<NullableBasicResult>({ url: `/api/v1/asset_devices/batchUpdate`, data });
}

/**
 * 删除设备
 * @param editId 设备id
 */
export function deleteDevice(editId: number) {
  return defHttp.delete<NullableBasicResult>({ url: `/api/v1/asset_devices/${editId}` });
}

/**
 * 获取设备筛选标签列表
 * @param params
 * @param params.isCloud 是否是云真机
 * @param params.online 是否在线
 */
export function getDeviceTagList(params?: { isCloud?: boolean; online?: boolean }) {
  return defHttp.get<DeviceTagGetResultModel>({ url: `/api/v1/asset_devices/tags`, params });
}

/**
 * 获取申请记录列表
 * @param params
 */
export function getApplyLogList(params?: ApplyLogListPageParams) {
  return defHttp.get<ApplyLogListGetResultModel>({ url: '/api/v1/asset_apply_logs', params });
}

/**
 * 创建申请记录
 * @param data 申请记录数据
 */
export function addApplyLog(data: ApplyLogListItem) {
  return defHttp.post<BasicAddResult>({ url: '/api/v1/asset_apply_logs', data });
}

/**
 * 编辑申请记录
 * @param data 申请记录数据
 * @param editId 申请记录id
 */
export function editApplyLog(data: ApplyLogListItem, editId: number) {
  return defHttp.put<NullableBasicResult>({ url: `/api/v1/asset_apply_logs/${editId}`, data });
}

/**
 * 删除申请记录
 * @param editId 申请记录id
 */
export function deleteApplyLog(editId: number) {
  return defHttp.delete<NullableBasicResult>({ url: `/api/v1/asset_apply_logs/${editId}` });
}

/**
 * 直接借出设备
 * @param data 申请记录数据
 */
export function directLendDevice(data: ApplyLogListItem) {
  return defHttp.post<BasicAddResult>({ url: '/api/v1/asset_apply_logs/direct', data });
}

/**
 * 确认领用设备
 * @param data 申请记录数据
 * @param editId 申请记录id
 */
export function confirmBorrowDevice(data: ApplyLogListItem, editId: number) {
  return defHttp.put<NullableBasicResult>({ url: `/api/v1/asset_apply_logs/${editId}/borrow`, data });
}

/**
 * 更改归还时间
 * @param data 更改归还时间数据
 * @param editId 申请记录id
 */
export function changeReturnTime(data: ChangeReturnTimeParams, editId: number) {
  return defHttp.put<NullableBasicResult>({ url: `/api/v1/asset_apply_logs/${editId}/time`, data }, { successMessageMode: 'none' });
}

/**
 * 获取使用记录列表
 * @param params
 */
export function getUsageLogList(params?: UsageLogListPageParams) {
  return defHttp.get<UsageLogListGetResultModel>({ url: '/api/v1/asset_usage_logs', params });
}

/**
 * 创建使用记录
 * @param data 使用记录数据
 */
export function addUsageLog(data: UsageLogListItem) {
  return defHttp.post<NullableBasicResult>({ url: '/api/v1/asset_usage_logs', data });
}

/**
 * 编辑使用记录
 * @param data 使用记录数据
 * @param editId 使用记录id
 */
export function editUsageLog(data: UsageLogListItem, editId: number) {
  return defHttp.put<NullableBasicResult>({ url: `/api/v1/asset_usage_logs/${editId}`, data });
}

/**
 * 删除使用记录
 * @param editId 使用记录id
 */
export function deleteUsageLog(editId: number) {
  return defHttp.delete<NullableBasicResult>({ url: `/api/v1/asset_usage_logs/${editId}` });
}

/**
 * 创建归还记录
 * @param data 归还记录数据
 */
export function addReturnLog(data: ApplyLogListItem) {
  return defHttp.post<NullableBasicResult>({ url: '/api/v1/asset_return_logs', data });
}

/**
 * 编辑归还记录
 * @param data 归还记录数据
 * @param editId 归还记录id
 */
export function editReturnLog(data: ApplyLogListItem, editId: number) {
  return defHttp.put<NullableBasicResult>({ url: `/api/v1/asset_return_logs/${editId}`, data });
}

/**
 * 直接归还设备
 * @param data 归还记录数据
 */
export function directReturnDevice(data: ApplyLogListItem) {
  return defHttp.post<BasicAddResult>({ url: '/api/v1/asset_return_logs/direct', data });
}

/**
 * 获取设备品牌列表
 * @param params
 */
export function getDeviceBrandsListByPage(params: BrandsListPageParams) {
  return defHttp.get<BrandsListGetResultModel>({ url: '/api/v1/asset/device/brands', params });
}

/**
 * 新增设备品牌
 * @param data 品牌数据
 */
export function addDeviceBrand(data: BrandsListItem) {
  return defHttp.post<BasicAddResult>({ url: '/api/v1/asset/device/brands', data });
}

/**
 * 获取芯片品牌列表
 * @param params
 */
export function getBrandsListByPage(params: BrandsListPageParams) {
  return defHttp.get<BrandsListGetResultModel>({ url: '/api/v1/soc/brands', params });
}

/**
 * 新增芯片品牌
 * @param data 品牌数据
 */
export function addBrand(data: BrandsListItem) {
  return defHttp.post<BasicAddResult>({ url: '/api/v1/soc/brands', data });
}

/**
 * 获取芯片性能天梯列表
 * @param params
 */
export function getChipsetsListByPage(params: ChipsetsListPageParams) {
  return defHttp.get<ChipsetsListGetResultModel>({ url: '/api/v1/soc/chipsets', params });
}

/**
 * 新增芯片
 * @param data 芯片数据
 */
export function addChipset(data: ChipsetsListItem) {
  return defHttp.post<BasicAddResult>({ url: '/api/v1/soc/chipsets', data });
}

/**
 * 获取海外芯片天梯图列表
 * @param params
 */
export function getOverseasChipsetsListByPage(params: OverseasChipsetsListPageParams) {
  return defHttp.get<OverseasChipsetsListGetResultModel>({ url: `/api/v1/oversea/chipsets`, params });
}

/**
 * 获取海外设备天梯图列表
 * @param params
 */
export function getOverseasDevListByPage(params: OverseasDevListPageParams) {
  return defHttp.get<OverseasDevListGetResultModel>({ url: `/api/v1/oversea/devices`, params });
}

/**
 * 获取海外设备品牌列表
 */
export function getOverseasDevBrandList() {
  return defHttp.get<OverseasDevBrandListGetResultModel>({ url: `/api/v1/oversea/devices/brand` });
}

/**
 * 导出设备列表
 */
export function exportDeviceList() {
  return defHttp.get<{ path: string }>({ url: `/api/v1/asset_devices/export` }, { successMessageMode: 'none' });
}

/**
 * 检测设备
 */
export function checkDevice() {
  return defHttp.get<CheckDeviceResultModel>({ url: `/api/v1/asset_devices/check` });
}

/**
 * 更新设备指定字段
 * @param id 设备id
 * @param data 更新设备指定字段数据
 */
export function updateDeviceField(id: number, data: UpdateDeviceFieldParams) {
  return defHttp.put<{ isUpdateAccessLevel: boolean }>({ url: `/api/v1/asset_devices/${id}/check`, data }, { successMessageMode: 'none', errorMessageMode: 'none' });
}

/**
 * 批量更新设备指定字段
 * @param list 更新设备指定字段数据列表
 */
export function updateDeviceFieldBatch(list: UpdateDeviceFieldParams[]) {
  return defHttp.put<BatchUpdateDeviceFieldResult>({ url: `/api/v1/asset_devices`, data: list }, { successMessageMode: 'none', errorMessageMode: 'none' });
}

/**
 * 获取申请记录日志列表
 * @param params 请求参数
 */
export function getDeviceOptLogListByPage(params: DeviceOptLogListPageParams) {
  const { operatorIDs, operationTypes, ...rest } = params;
  const query = qs.stringify({ operatorIDs, operationTypes }, { arrayFormat: 'repeat' });
  return defHttp.get<DeviceOptLogListGetResultModel>({ url: `/api/v1/asset_op_logs${query ? `?${query}` : ''}`, params: rest });
}

/**
 * 设备订阅
 * @param deviceID 设备id
 * @param deviceType 设备类型
 */
export function subscribeDevice(deviceID: number, deviceType: DeviceCategoryTypeEnum) {
  return defHttp.post<BasicAddResult>({ url: `/api/v1/asset/device_subscribes`, data: { deviceID, deviceType } }, { successMessageMode: 'none' });
}

/**
 * 取消设备订阅
 * @param deviceID 设备id
 */
export function unsubscribeDevice(deviceID: number, deviceType: DeviceCategoryTypeEnum) {
  return defHttp.delete<NullableBasicResult>({ url: `/api/v1/asset/device_subscribes/${deviceID}`, data: { deviceType } }, { successMessageMode: 'none' });
}

/**
 * 获取设备筛选模板列表
 * @param params
 */
export function getDeviceFilterTemplateList(params: BasicPageParams) {
  return defHttp.get<getDeviceFilterTemplateListResultModel>({ url: `/api/v1/asset/device/filter_templates`, params });
}

/**
 * 新增设备筛选模板
 * @param data 设备筛选模板数据
 */
export function addDeviceFilterTemplate(data: FilterTemplateListItem) {
  return defHttp.post<BasicAddResult>({ url: `/api/v1/asset/device/filter_templates`, data });
}

/**
 * 删除设备筛选模板
 * @param editId 设备筛选模板id
 */
export function deleteDeviceFilterTemplate(editId: number) {
  return defHttp.delete<NullableBasicResult>({ url: `/api/v1/asset/device/filter_templates/${editId}` });
}

/**
 * 获取设备管理员列表
 */
export function getDeviceAdminList(params: BasicPageParams) {
  return defHttp.get<DeviceAdminListGetResultModel>({ url: `/api/v1/asset/device/admins`, params });
}

/**
 * 新增设备管理员
 * @param data 设备管理员数据
 */
export function addDeviceAdmin(data: DeviceAdminListItem) {
  return defHttp.post<BasicAddResult>({ url: `/api/v1/asset/device/admins`, data });
}

/**
 * 编辑设备管理员
 * @param data 设备管理员数据
 * @param editId 设备管理员id
 */
export function editDeviceAdmin(data: DeviceAdminListItem, editId: number) {
  return defHttp.put<NullableBasicResult>({ url: `/api/v1/asset/device/admins/${editId}`, data });
}

/**
 * 删除设备管理员
 * @param editId 设备管理员id
 */
export function deleteDeviceAdmin(editId: number) {
  return defHttp.delete<NullableBasicResult>({ url: `/api/v1/asset/device/admins/${editId}` });
}

/**
 * 获取设备管理员设备列表
 * @param params
 */
export function getDeviceAdminDeviceList(params: DeviceAdminDeviceListParams) {
  return defHttp.get<DeviceListGetResultModel>({ url: `/api/v1/asset/device/admins/deviceList`, params });
}

/**
 * 批量回收或取消回收设备
 * @param params
 */
export function recycleDevice(params: BatchRecycleDeviceParams) {
  return defHttp.put<BasicResult<{ inApproveDeviceIds: number }>>({ url: `/api/v1/asset/device/admins/recycle`, data: params });
}

/**
 * 判断是否是设备管理员
 */
export function isDeviceAdmin() {
  return defHttp.get<{ isAdmin: boolean }>({ url: `/api/v1/asset/device/admins/is_admin` });
}
/**
 * 获取设备【资产用途】选项列表
 */
export function getUsageList() {
  return defHttp.get<UsageListGetResultModel>({ url: `/api/v1/asset_devices/options/usage` });
}

/**
 * 拉取设备报障问题类型
 */
export function getFaultTypes() {
  return defHttp.get<FaultTypeListGetResultModel>({ url: `/api/v1/asset/faults/type` });
}

/**
 * 设备报障提交
 * @param data 设备报障数据
 */
export function createFault(data: FaultListItem) {
  return defHttp.post<BasicAddResult>({ url: `/api/v1/asset/faults`, data }, { successMessageMode: 'none' });
}

/**
 * 获取设备报障列表
 * @param params 请求参数
 */
export function getFaultListByPage(params: BasicPageParams & Partial<FaultListItem>) {
  return defHttp.get<FaultListGetResultModel>({ url: `/api/v1/asset/faults`, params });
}

/**
 * 更新设备报障状态
 * @param id 设备报障id
 * @param status 设备报障状态
 */
export function updateFaultStatus(id: number, status: number) {
  return defHttp.put<NullableBasicResult>({ url: `/api/v1/asset/faults/${id}`, data: { status } });
}

/**
 * 更新设备管理员最长借用天数
 * @param id 设备管理员id
 * @param data 更新设备管理员最长借用天数数据
 */
export function updateDeviceAdminMaxDay(id: number, data: Pick<DeviceAdminListItem, 'maxDay' | 'subAdminIds'>) {
  return defHttp.put<NullableBasicResult>({ url: `/api/v1/asset/device/admins/${id}/day`, data });
}

/**
 * 查询设备视图
 */
export function getAssetDevicesViewApi() {
  return defHttp.get<{ view: string }>({ url: `/api/v1/asset_devices/view` });
}

/**
 * 保存设备视图
 */
export function setAssetDevicesViewApi(params: { view: string }) {
  return defHttp.put<NullableBasicResult>({ url: `/api/v1/asset_devices/view`, data: params }, { successMessageMode: 'none' });
}

/**
 * 获取部门设备默认流通级别配置列表
 */
export function getDeptDeviceAccessLevelList(params: { deptID: number;page: number;pageSize: number }) {
  return defHttp.get<BasicFetchResult<AccessLevelList>>({ url: `/api/v1/asset/device/accessLevel`, params });
}

export function setDeptDeviceAccessLevelList(params: { deptID: number; accessLevel: number; deptIds?: number[]; projectIds?: number[] }) {
  return defHttp.post<NullableBasicResult>({ url: `/api/v1/asset/device/accessLevel`, data: params }, { successMessageMode: 'none' });
}
/**
 * 获取指定部门的默认流通级别
 */
export function getDeptDeviceAccessLevel(deptID: number) {
  return defHttp.get<{ deptAccessLevel: AccessLevelList }>({ url: `/api/v1/asset/device/accessLevel/${deptID}` });
}
/**
 * 获取udid是否存在
 */
export function getUdidIsExist(udidID: string) {
  return defHttp.get<NullableBasicResult>({ url: `/api/v1/asset_devices/udid/${udidID}` }, { errorMessageMode: 'none' });
}
/**
 * 批量设备直接借出
 */
export function devicesApplyBatchDirect(params: { deviceIds: number[]; proposerID: number; returnTime: number }) {
  return defHttp.post<NullableBasicResult>({ url: `/api/v1/asset_apply_logs/batch_direct`, data: params });
}
/**
 * 批量设备直接归还
 */
export function devicesReturnBatchDirect(params: { deviceIds: number[]; remark: string }) {
  return defHttp.post<NullableBasicResult>({ url: `/api/v1/asset_return_logs/batch_direct`, data: params });
}
