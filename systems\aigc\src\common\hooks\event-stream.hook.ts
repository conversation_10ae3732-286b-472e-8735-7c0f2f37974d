import { onUnmounted, ref } from 'vue';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { getItem, STORAGE_KEY } from '../utils/localstorage';
import { useMicroAppInject, useRouteNavigationCtx } from '@hg-tech/oasis-common';
import { GlobalEnv } from '@/configs/global-env';
import { Logger } from '../utils/logger';
import { type ChatMessageSearch, ChatMessageStatus } from '@/models/chat';
import { safeParse } from '../utils/utils';
import { useUserBase } from './user-base.hook';
import { LOGIN_STATE } from '../constants/login.constant';
import { useNaiveUIApi } from './naive-api.hook';
import { router } from '@/plugins/router';
import { PageName } from '@/configs/page-config';
import { safeEncodeURL } from '../utils/url/compressor';
import CustomError from '../utils/custom-error';

interface StreamOptions {
  url: string;
  apiPrefix?: string;
  headers?: Record<string, string>;
  interceptors?: {
    request?: (url: string, params: Record<string, any>, headers: Record<string, string>) => { url: string; params: Record<string, any>; headers: Record<string, string> };
    response?: (data: ChunkData) => ChunkData;
  };
}

interface ChunkData {
  content: string;
  reasoning_content: string;
  status: ChatMessageStatus;
  search: ChatMessageSearch[];
}

export function useEventStream(options: StreamOptions) {
  const message = ref<string>(''); // 存储流式数据
  const loading = ref(false); // 是否正在流式请求
  const thinkDone = ref(false); // 是否推理完成
  let controller: AbortController | null = null;
  const API_PREFIX = `${GlobalEnv.APIPrefixMap[GlobalEnv.HGEnv]}/aigc${GlobalEnv.HGEnv === 'dev' ? '-api' : ''}`;

  // **彻底终止请求**
  const abort = () => {
    if (controller) {
      controller.abort();
      controller = null;
      thinkDone.value = false;
      loading.value = false;
    }
  };

  const onHandleUnauthorized = async () => {
    // 401错误，跳转登录
    const { loginState } = useUserBase();
    const { message } = useNaiveUIApi();
    loginState.value = LOGIN_STATE.LOGIN_FAILED;
    message.warning('登录状态已失效，即将跳转登录页');
    await new Promise((resolve) => setTimeout(resolve, 2000));
    if (window.__MICRO_APP_ENVIRONMENT__) {
      const { data } = useMicroAppInject(useRouteNavigationCtx);
      data.value?.onUnauthorized('登录失败，请重新登录');
    } else {
      router.push({
        name: PageName.Login,
        query: {
          redirect: safeEncodeURL(location.pathname + location.search),
        },
      });
    }
  };

  const start = (
    params: {
      data?: Record<string, any>;
      onSearch?: (data: ChatMessageSearch[]) => void;
      onMessage?: (data: string) => void;
      onError?: (error: Error) => void;
      onFinished?: () => void;
    },
  ) => {
    if (loading.value) {
      console.warn('Stream is already running.');
      return;
    }

    controller = new AbortController();
    loading.value = true;
    message.value = ''; // 清空上次的数据

    if (!getItem(STORAGE_KEY.TECH_TOKEN)) {
      onHandleUnauthorized();
      return;
    }

    let { url, headers, interceptors } = options;

    // 请求拦截
    if (interceptors?.request) {
      ({ url, params, headers } = interceptors.request(url, params, headers || {}));
    }

    // 拼接 URL 参数
    const fullUrl = (options.apiPrefix ? options.apiPrefix : API_PREFIX) + url;

    fetchEventSource(fullUrl, {
      method: 'POST',
      headers: {
        'X-token': getItem(STORAGE_KEY.TECH_TOKEN) as string,
        'Content-Type': 'application/json',
        ...headers,
      },
      body: JSON.stringify(params.data),
      openWhenHidden: true,
      signal: controller.signal,
      fetch: async (url, options) => {
        const res = await fetch(url, options);
        const contentType = res.headers.get('content-type') || '';
        if (!contentType.includes('text/event-stream')) {
          if (res.status === 200) {
            /**
             * 200场景非流式响应 => token过期
             * @see https://applink.feishu.cn/client/message/link/open?token=Amcq2cQn0YAEaJxVhs2DgAE%3D
             */
            onHandleUnauthorized();
          } else {
            // 处理非流式响应及特殊状态码
            throw new CustomError(`Unexpected response status: ${JSON.stringify(res)}`, true);
          }
        }
        return res;
      },
      async onmessage(event) {
        if (!event.data) {
          abort();
          return;
        }
        let chunkData = safeParse(event.data) as ChunkData;
        // 如果返回了结束标记，终止流
        if (chunkData.status === ChatMessageStatus.Finish) {
          Logger.debug('Stream completed.');
          abort(); // **终止流，防止空消息继续返回**
          params.onFinished?.();
          return;
        }

        // 响应拦截
        if (interceptors?.response) {
          chunkData = interceptors.response(chunkData);
        }

        const chunkMsg = chunkData.content ?? '';
        const thinkMsg = chunkData.reasoning_content ?? '';
        const search = chunkData.search;
        if (search && search.length > 0) {
          // 处理搜索结果
          params.onSearch?.(search);
        }

        if (thinkMsg) {
          // 如果有推理数据，追加换行符
          if (!message.value.includes('> ')) {
            message.value += `> ${thinkMsg}`;
            params.onMessage?.(`> ${thinkMsg}`);
          } else if (thinkMsg.includes('\n')) {
            const thinkChunk = thinkMsg.split('\n');
            if (thinkChunk.every((item) => item.trim() === '')) {
              message.value += `\n > \n > `;
              params.onMessage?.(`\n > \n > `);
              return;
            }
            if (thinkChunk[0].trim() === '') {
              // 如果第一行为空，需要为后续补全markdown格式
              thinkChunk.filter((item) => item.trim()).forEach((line) => {
                message.value += `\n > \n > ${line.trim()}`;
                params.onMessage?.(`\n > \n > ${line.trim()}`);
              });
            } else {
              // 如果第一行不为空，需要直接追加内容，再处理markdown格式
              thinkChunk.filter((item) => item.trim()).forEach((line) => {
                message.value += `${line.trim()}\n > \n > `;
                params.onMessage?.(` ${line.trim()}\n > \n > `);
              });
            }
          } else {
            message.value += thinkMsg;
            params.onMessage?.(thinkMsg);
          }
        }
        if (chunkMsg) {
          if (!thinkMsg && !thinkDone.value) {
            thinkDone.value = true;
            message.value += `\n\n${chunkMsg}`;
            params.onMessage?.(`\n\n${chunkMsg}`);
            return;
          }
          message.value += chunkMsg;
          params.onMessage?.(chunkMsg);
        }
      },
      onerror(error) {
        console.error('Stream error:', error);
        loading.value = false;
        thinkDone.value = false;
        // 触发外部错误回调
        params.onError?.(error);
        abort();
        // throw异常避免无限自带的继续重连逻辑
        throw error;
      },
      onclose() {
        loading.value = false;
        thinkDone.value = false;
      },
    });
  };

  // 组件卸载时自动终止流
  onUnmounted(() => {
    abort();
  });

  return { message, loading, start, abort };
}
