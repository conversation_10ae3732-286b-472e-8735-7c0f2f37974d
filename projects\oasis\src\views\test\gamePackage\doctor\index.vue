<template>
  <div class="game-package-doctor">
    <div v-if="doctorData" class="game-package-doctor__title">
      <div>『<Icon :icon="getPlatformIconByVal(doctorData?.platform)" :size="26" />{{ doctorData?.game?.name }}({{ doctorData?.version?.version }})』的包体检测报告</div>
      <BasicButton shape="round" type="primary" ghost :loading="loading" class="absolute right-30px c-FO-Content-Text1!" @click="exportDoctorReport">
        {{ loading ? '正在导出' : '导出飞书文档' }}
      </BasicButton>
    </div>
    <div class="flex">
      <!--  锚点 begin  -->
      <div class="game-package-doctor__anchor">
        <Anchor :offsetTop="200" :anchorList="anchorList" :titleOffsetTop="0" lineColor="#cdcdcd" />
      </div>
      <!--  锚点 end  -->
      <div class="game-package-doctor__content">
        <div :id="anchorList[0]" class="game-package-doctor__card">
          <div class="game-package-doctor__card-title">
            {{ anchorList[0] }}
          </div>
          <Proportion class="self-center" @getDoctorData="getDoctorData" />
        </div>
        <template v-if="doctorData">
          <div :id="anchorList[1]" class="game-package-doctor__card">
            <div class="game-package-doctor__card-title">
              {{ anchorList[1] }}
            </div>
            <RenderTable :columns="duplicateColumns" :tableData="doctorData?.doctor?.duplicate" />
          </div>
          <div :id="anchorList[2]" class="game-package-doctor__card">
            <div class="game-package-doctor__card-title">
              {{ anchorList[2] }}
            </div>
            <RenderTable :columns="modelMapColumns" :tableData="doctorData?.doctor?.uncompressed" />
          </div>
          <div :id="anchorList[3]" class="game-package-doctor__card">
            <div class="game-package-doctor__card-title">
              {{ anchorList[3] }}
            </div>
            <RenderTable :columns="modelMapColumns" :tableData="doctorData?.doctor?.big_size" />
          </div>
          <div :id="anchorList[4]" class="game-package-doctor__card">
            <div class="game-package-doctor__card-title">
              {{ anchorList[4] }}
            </div>
            <RenderTable :columns="solidColorColumns" :tableData="doctorData?.doctor?.solid_color_image" />
          </div>
          <div :id="anchorList[5]" class="game-package-doctor__card">
            <div class="game-package-doctor__card-title">
              {{ anchorList[5] }}
            </div>
            <RenderTable :columns="largeMeshAndAnimationColumns" :tableData="doctorData?.doctor?.large_mesh" />
          </div>
          <div :id="anchorList[6]" class="game-package-doctor__card">
            <div class="game-package-doctor__card-title">
              {{ anchorList[6] }}
            </div>
            <RenderTable :columns="largeMeshAndAnimationColumns" :tableData="doctorData?.doctor?.large_animation" />
          </div>
        </template>
      </div>
    </div>
    <Modal v-model:open="modalOpen" centered :maskClosable="false">
      <template #title>
        <div class="flex items-center justify-center">
          <div>导出至飞书文档</div>
        </div>
      </template>

      <div v-if="urlError" class="h-100px flex items-center justify-center">
        数据导出至飞书文档失败，点击重试按钮再次尝试。
      </div>
      <div v-else class="h-100px flex items-center justify-center">
        数据已导出至飞书文档，点击前往按钮查看。
      </div>

      <template #footer>
        <div class="flex items-center justify-center">
          <BasicButton type="primary" @click="handleOk">
            {{ urlError ? '重试' : '前往' }}
          </BasicButton>
          <BasicButton @click="modalOpen = false">
            取消
          </BasicButton>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { anchorList } from './doctor.data';
import { BasicButton } from '/@/components/Button';
import type { GamePackageDoctorListItem } from '/@/api/page/model/testModel';
import { Anchor } from '/@/components/Anchor';
import Icon from '/@/components/Icon';

import Proportion from '/@/views/test/gamePackage/doctor/proportion/index.vue';
import { platformOptions } from '/@/views/test/gamePackage/settings/settings.data';
import RenderTable from './RenderTable.vue';
import { duplicateColumns, largeMeshAndAnimationColumns, modelMapColumns, solidColorColumns } from './renderTableColumns.tsx';
import { exportDoctorAnalysesApi } from '/@/api/page/test.ts';
import { useUserStore } from '/@/store/modules/user';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { message, Modal } from 'ant-design-vue';
import { sendEvent } from '/@/service/tracker/index.ts';
import { useRouter } from 'vue-router';
import { GamePackageCenterTrackTrigger } from '/@/api/page/model/testModel';

const { currentRoute } = useRouter();
const { execute, loading } = useLatestPromise(exportDoctorAnalysesApi);
const doctorData = ref<GamePackageDoctorListItem>();
const modalOpen = ref(false);
const userStore = useUserStore();
const urlError = ref(false);
const url = ref('');
const oasis = Number(currentRoute.value.query?.oasis || 0);
const packageID = Number(currentRoute.value.query?.packageID || 0);

function getDoctorData(doctor: GamePackageDoctorListItem) {
  doctorData.value = doctor;
}

function getPlatformIconByVal(val: number | undefined) {
  return platformOptions.find((e) => e.value === val)?.icon;
}
async function exportDoctorReport() {
  sendEvent('game_package_center_export_report', {
    game_package_center_trigger_channel: !oasis ? GamePackageCenterTrackTrigger.Web : GamePackageCenterTrackTrigger.Oasis,
    game_package_center_package_id: packageID,
    game_package_center_branch_id: doctorData.value?.pkgID,
  });

  if (!userStore.getProjectId || !doctorData.value?.ID) {
    return;
  }
  try {
    const res = await execute(userStore.getProjectId!, doctorData.value?.ID);
    if (res?.code === 7) {
      urlError.value = true;
    } else {
      urlError.value = false;
      url.value = res?.url || '';
      message.success('导出成功，正在跳转');
    }
  } catch {
    urlError.value = true;
  } finally {
    modalOpen.value = true;
  }
}

function handleOk() {
  if (urlError.value) {
    exportDoctorReport();
  } else {
    window.open(url.value, '_blank');
  }
  modalOpen.value = false;
}
</script>

<style lang="less" scoped>
.game-package-doctor {
  position: relative;

  &__anchor {
    width: 8vw;
    margin: 100px auto 0;
  }

  &__content {
    width: 78vw;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  &__title {
    font-size: 20px;
    font-weight: bold;
    margin: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__card {
    background-color: @FO-Container-Background;
    margin: 0 16px 16px;
    border-radius: 16px;
    padding: 0 16px 32px;
    width: 1000px;
    display: flex;
    flex-direction: column;

    &-title {
      width: 100%;
      font-size: 16px;
      font-weight: bold;
      padding: 16px;
      border-bottom: 1px solid;
      border-bottom-color: @FO-Container-Stroke1;
      position: relative;
      margin-bottom: 16px;

      &::before {
        position: absolute;
        content: '';
        width: 4px;
        height: 26px;
        background-color: @FO-Brand-Primary-Default;
        left: 4px;
        top: 16px;
      }
    }
  }
}
</style>
