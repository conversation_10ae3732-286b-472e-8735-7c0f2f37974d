import type { RuleV1FeishuItemOperation } from '@hg-tech/api-schema-merge';
import { Button, FormItem, Select } from 'ant-design-vue';
import { type FeishuWorkItem, FeishuWorkItemOptions, RuleV1FeishuItemOperationOptions } from '../../../models/config.model';
import { type PropType, computed, defineComponent } from 'vue';
import Icon from '@ant-design/icons-vue';
import { DragItem } from '../../../components/DragItem';

import SystemStrokeDrag from '../../../assets/svg/SystemStrokeDrag.svg?component';
import Add from '../../../assets/svg/Add.svg?component';
import Substract from '../../../assets/svg/Substract.svg?component';
import { useMergeHome } from '../use-merge-home';

const FeishuRuleItem = defineComponent({
  props: {
    domain: {
      type: String as PropType<string>,
      default: '',
    },
    feishuItemOperation: {
      type: String as PropType<RuleV1FeishuItemOperation>,
      default: undefined,
    },
    workItemType: {
      type: String as PropType<FeishuWorkItem>,
      default: undefined,
    },
    fieldKey: {
      type: String as PropType<string>,
      default: undefined,
    },
    optionValue: {
      type: Array as PropType<string[]>,
      default: undefined,
    },
    index: {
      type: Number as PropType<number>,
      default: 0,
    },
    onRemove: {
      type: Function as PropType<(index: number) => void>,
      required: true,
    },
    onAdd: {
      type: Function as PropType<(index: number) => void>,
      required: true,
    },
  },
  emits: {
    'update:feishuItemOperation': (_?: RuleV1FeishuItemOperation) => true,
    'update:fieldKey': (_?: string) => true,
    'update:optionValue': (_?: string[]) => true,
    'update:workItemType': (_?: FeishuWorkItem) => true,
  },
  setup(props, { emit }) {
    const { feishuTagMap } = useMergeHome();
    const feishuItemOperation = computed({
      get: () => props.feishuItemOperation,
      set: (value) => {
        emit('update:feishuItemOperation', value);
      },
    });

    const workItemType = computed({
      get: () => props.workItemType,
      set: (value) => {
        emit('update:workItemType', value);
      },
    });

    const fieldKey = computed({
      get: () => props.fieldKey,
      set: (value) => {
        emit('update:fieldKey', value);
      },
    });

    const optionValue = computed({
      get: () => props.optionValue,
      set: (value) => {
        emit('update:optionValue', value);
      },
    });

    // 当前工作项类型对应的飞书标签选项
    const currentFeishuTagMapOptions = computed(() => {
      if (!workItemType.value) {
        return [];
      }
      return feishuTagMap.value?.[workItemType.value]?.map((field) => ({
        label: field.fieldName,
        value: field.fieldKey,
      })) ?? [];
    });

    // 当前飞书标签对应的飞书标签可选项
    const currentFeishuValueOptions = computed(() => {
      if (!fieldKey.value || !workItemType.value) {
        return [];
      }
      const currentField = feishuTagMap.value?.[workItemType.value]?.find((field) => field.fieldKey === fieldKey.value);
      if (!currentField) {
        return [];
      }
      return currentField.Options?.map((option) => ({
        label: option.label,
        value: option.value,
      })) ?? [];
    });

    const rules = {
      workItemType: [{ required: true, message: '请选择工作项类型' }],
      feishuItemOperation: [{ required: true, message: '请选择运算符' }],
      fieldKey: [{ required: true, message: '请选择字段' }],
      optionValue: [{ required: true, message: '请选择选项值' }],
    };

    return () => (
      <DragItem class="w-full flex gap-12px rd-6px pr-8px pt-8px" dragClass="drag-handle">
        {{
          deaultDrag: ({ isHover }: { isHover: boolean }) => (
            <FormItem>
              <div
                class="drag-handle w-50px flex justify-center"
              >
                {
                  isHover
                    ? (
                      <Icon
                        class="cursor-move font-size-18px"
                        component={<SystemStrokeDrag />}
                      />
                    )
                    : <div class="FO-Font-B14 h-24px w-24px flex justify-center rd-full bg-FO-Datavis-Violet3 c-FO-Content-Text1">{props.index + 1}</div>
                }
              </div>
            </FormItem>
          ),
          default: () => (
            <>
              <FormItem>若</FormItem>
              <FormItem class="w-120px" name={[props.domain, props.index, 'workItemType']} rules={rules.workItemType}>
                <Select
                  allowClear
                  placeholder="工作项类型"
                  v-model:value={workItemType.value}
                >
                  {FeishuWorkItemOptions.map((rule) => (
                    <Select.Option key={rule.value} value={rule.value}>
                      {rule.label}
                    </Select.Option>
                  ))}
                </Select>
              </FormItem>
              <FormItem class="w-180px" name={[props.domain, props.index, 'fieldKey']} rules={rules.fieldKey}>
                <Select
                  allowClear
                  onChange={() => {
                    optionValue.value = undefined; // 清空选项值
                  }}
                  placeholder="选择字段"
                  v-model:value={fieldKey.value}
                >
                  {currentFeishuTagMapOptions.value.map((rule) => (
                    <Select.Option key={rule.value} value={rule.value}>
                      {rule.label}
                    </Select.Option>
                  ))}
                </Select>
              </FormItem>
              <FormItem class="w-120px" name={[props.domain, props.index, 'feishuItemOperation']} rules={rules.feishuItemOperation}>
                <Select
                  allowClear
                  placeholder="选择运算符"
                  v-model:value={feishuItemOperation.value}
                >
                  {RuleV1FeishuItemOperationOptions.map((rule) => (
                    <Select.Option key={rule.value} value={rule.value}>
                      {rule.label}
                    </Select.Option>
                  ))}
                </Select>
              </FormItem>
              <FormItem class="flex-1" name={[props.domain, props.index, 'optionValue']} rules={rules.optionValue}>
                <Select
                  allowClear
                  mode="multiple"
                  placeholder="选择字段的值"
                  showSearch={false}
                  v-model:value={optionValue.value}
                >
                  {currentFeishuValueOptions.value.map((rule) => (
                    <Select.Option key={rule.value} value={rule.value}>
                      {rule.label}
                    </Select.Option>
                  ))}
                </Select>
              </FormItem>
              <FormItem class="flex-shrink-0">
                则不合并
              </FormItem>
              <FormItem>
                <div class="flex items-center gap-8px">
                  <Button
                    class="btn-fill-default"
                    icon={(
                      <Icon component={<Substract />} />
                    )}
                    onClick={() => props.onRemove(props.index)}
                    type="text"
                  />
                  <Button
                    class="btn-fill-default"
                    icon={(
                      <Icon component={<Add />} />
                    )}
                    onClick={() => props.onAdd(props.index)}
                    type="text"
                  />
                </div>
              </FormItem>
            </>
          ),
        }}
      </DragItem>
    );
  },
});

export {
  FeishuRuleItem,
};
