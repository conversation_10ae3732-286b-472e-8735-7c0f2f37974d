import { Button, FormItem, Select } from 'ant-design-vue';
import { AdditionItemFieldOptions, AdditionItemOperationOptions } from '../../../models/config.model';
import { type PropType, computed, defineComponent } from 'vue';
import Icon from '@ant-design/icons-vue';
import { DragItem } from '../../../components/DragItem';
import { ForgeonUserSelector } from '@hg-tech/oasis-common';
import { useUserListOption } from '../../../composables/useUserSearch';
import type { RuleV1AdditionalHandlerField, RuleV1HandlerOperation, UserUser } from '@hg-tech/api-schema-merge';

import SystemStrokeDrag from '../../../assets/svg/SystemStrokeDrag.svg?component';
import Add from '../../../assets/svg/Add.svg?component';
import Substract from '../../../assets/svg/Substract.svg?component';
import { uniqBy } from 'lodash';

const AdditionResolverItem = defineComponent({
  props: {
    domain: {
      type: String as PropType<string>,
      default: '',
    },
    index: {
      type: Number as PropType<number>,
      default: -1,
    },
    operation: {
      type: String as PropType<RuleV1HandlerOperation>,
    },
    field: {
      type: String as PropType<RuleV1AdditionalHandlerField>,
    },
    user: {
      type: String as PropType<string>,
    },
    handler: {
      type: Array as PropType<string[]>,
    },
    userList: {
      type: Array as PropType<UserUser[]>,
      default: () => [],
    },
    handlerList: {
      type: Array as PropType<UserUser[]>,
      default: () => [],
    },
    onRemove: {
      type: Function as PropType<(index: number) => void>,
      required: true,
    },
    onAdd: {
      type: Function as PropType<(index: number) => void>,
      required: true,
    },
  },
  emits: {
    'update:operation': (_?: RuleV1HandlerOperation) => true,
    'update:field': (_?: RuleV1AdditionalHandlerField) => true,
    'update:user': (_?: string) => true,
    'update:handler': (_?: string[]) => true,
  },
  setup(props, { emit }) {
    const { userListOptions, userListLoading, queryUser, resetUserList } = useUserListOption(
      computed(() => uniqBy([
        ...(props.userList ?? []),
        ...(props.handlerList ?? []),
      ], 'hgAccount')),
    );
    const operation = computed({
      get: () => props.operation,
      set: (value) => {
        emit('update:operation', value);
      },
    });
    const field = computed({
      get: () => props.field,
      set: (value) => {
        emit('update:field', value);
      },
    });
    const user = computed({
      get: () => props.user,
      set: (value) => {
        emit('update:user', value);
      },
    });
    const handler = computed({
      get: () => props.handler,
      set: (value) => {
        emit('update:handler', value);
      },
    });

    const rules = {
      field: [{ required: true, message: '请选择字段' }],
      operation: [{ required: true, message: '请选择运算符' }],
      user: [{ required: true, message: '请选择人员' }],
      handler: [{ required: true, message: '请选择处理人' }],
    };

    return () => (
      <DragItem class="w-full flex gap-12px rd-6px pr-8px pt-8px" dragClass="drag-handle">
        {{
          deaultDrag: ({ isHover }: { isHover: boolean }) => (
            <FormItem>
              <div
                class="drag-handle w-50px flex justify-center"
              >
                {
                  isHover
                    ? (
                      <Icon
                        class="cursor-move font-size-18px"
                        component={<SystemStrokeDrag />}
                      />
                    )
                    : <div class="FO-Font-B14 h-24px w-24px flex justify-center rd-full bg-FO-Datavis-Violet3 c-FO-Content-Text1">{props.index + 1}</div>
                }
              </div>
            </FormItem>
          ),
          default: () => (
            <>
              <FormItem>若</FormItem>
              <FormItem class="w-100px" name={[props.domain, props.index, 'field']} rules={rules.field}>
                <Select
                  allowClear
                  placeholder="选择字段"
                  v-model:value={field.value}
                >
                  {AdditionItemFieldOptions.map((rule) => (
                    <Select.Option key={rule.value} value={rule.value}>
                      {rule.label}
                    </Select.Option>
                  ))}
                </Select>
              </FormItem>
              <FormItem class="w-120px" name={[props.domain, props.index, 'operation']} rules={rules.operation}>
                <Select
                  allowClear
                  placeholder="选择运算符"
                  v-model:value={operation.value}
                >
                  {AdditionItemOperationOptions.map((rule) => (
                    <Select.Option key={rule.value} value={rule.value}>
                      {rule.label}
                    </Select.Option>
                  ))}
                </Select>
              </FormItem>
              <FormItem class="w-150px" name={[props.domain, props.index, 'user']} rules={rules.user}>
                <ForgeonUserSelector
                  loading={userListLoading.value}
                  maxTagTextLength={5}
                  multiple
                  onReset={resetUserList}
                  onSearch={(params) => {
                    queryUser(params, {});
                  }}
                  onUpdate:value={(v) => {
                    user.value = v.length ? v.pop() : undefined;
                  }}
                  options={userListOptions.value}
                  placeholder="选择人员(单选)"
                  value={user.value}
                />
              </FormItem>
              <FormItem class="whitespace-nowrap">
                则添加
              </FormItem>
              <FormItem class="w-150px" name={[props.domain, props.index, 'handler']} rules={rules.handler}>
                <ForgeonUserSelector
                  loading={userListLoading.value}
                  maxTagTextLength={5}
                  multiple
                  onReset={resetUserList}
                  onSearch={(params) => {
                    queryUser(params, {});
                  }}
                  options={userListOptions.value}
                  placeholder="选择人员(多选)"
                  v-model:value={handler.value}
                />
              </FormItem>
              <FormItem class="whitespace-nowrap">
                为合并失败处理人
              </FormItem>
              <FormItem>
                <div class="flex items-center gap-8px">
                  <Button
                    class="btn-fill-default"
                    icon={(
                      <Icon component={<Substract />} />
                    )}
                    onClick={() => props.onRemove(props.index)}
                    type="text"
                  />
                  <Button
                    class="btn-fill-default"
                    icon={(
                      <Icon component={<Add />} />
                    )}
                    onClick={() => props.onAdd(props.index)}
                    type="text"
                  />
                </div>
              </FormItem>
            </>
          ),
        }}
      </DragItem>
    );
  },
});

export {
  AdditionResolverItem,
};
