import Icon from '@ant-design/icons-vue';
import { But<PERSON>, Drawer, Form, message } from 'ant-design-vue';
import { type PropType, computed, defineComponent, ref, watch } from 'vue';
import type { MergeV1MergeRecord, MergeV1MergeServiceUpdateHandlerBody } from '@hg-tech/api-schema-merge';
import { DrawerHeader } from '../components/drawer-header';
import { useUserListOption } from '../../../composables/useUserSearch';
import type { Rule } from 'ant-design-vue/es/form';
import { ForgeonUserSelector } from '@hg-tech/oasis-common';
import type { SysUserInfo } from '../../../api/user';
import { useMergeTask } from '../use-merge-task';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { mergeApi } from '../../../api';
import { traceCustomEvent } from '../../../services/track';
import { TrackEventName } from '../../../constants/event';

import Close from '../../../assets/svg/Close.svg?component';

const AssignResolver = defineComponent({
  props: {
    visible: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    data: {
      default: () => ({}),
      type: Object as PropType<MergeV1MergeRecord>,
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const {
      userListOptions,
      userListLoading,
      queryUser,
      resetUserList,
    } = useUserListOption(
      computed(() => props.data.handler?.map((i) => ({
        hgAccount: i.hgAccount,
        name: i.name,
        nickname: i.nickname,
        avatar: i.avatar,
      })) as SysUserInfo[] || []),
    );
    const { currentProjectId, fetchTaskList, currentRuleId, currentBranchMap, ruleList } = useMergeTask();
    const { execute: updateHandler } = useLatestPromise(mergeApi.v1.mergeServiceUpdateHandler);

    const formRef = ref();
    const form = ref<{
      resolvers: string[];
    }>({
      resolvers: [],
    });
    const rules: Record<string, Rule[]> = {
      resolvers: [
        {
          required: true,
          message: '请选择处理人',
        },
      ],
    };

    const open = computed({
      get: () => props.visible,
      set: (value) => {
        emit('update:visible', value);
      },
    });
    const currentRule = computed(() => ruleList.value.find((rule) => rule.id === currentRuleId.value));

    const onUpdateHandler = async (params: MergeV1MergeServiceUpdateHandlerBody) => {
      if (!currentProjectId.value) {
        return;
      }
      await updateHandler({ id: currentProjectId.value }, params);
      fetchTaskList();
    };

    const onClose = () => {
      open.value = false;
    };

    const onSubmit = async () => {
      await formRef.value.validate();
      traceCustomEvent(TrackEventName.conflux_handler_assign, {
        conflux_rule_id: currentRuleId.value,
        conflux_source_branch_path: currentRule.value?.sourceStreamId ? currentBranchMap.value.get(currentRule.value?.sourceStreamId)?.path : undefined,
        conflux_target_branch_path: currentRule.value?.targetStreamId ? currentBranchMap.value.get(currentRule.value?.targetStreamId)?.path : undefined,
      });
      await onUpdateHandler({
        recordId: props.data.id,
        handler: form.value.resolvers,
      });
      message.success('分配成功');
      open.value = false;
    };

    watch(() => props.visible, (newVal) => {
      if (newVal && props.data.handler?.length) {
        form.value.resolvers = props.data.handler?.map((i) => i.hgAccount ?? '') || [];
      }
    });

    return () => (
      <Drawer
        bodyStyle={{ padding: '24px', overflow: 'hidden' }}
        closable={false}
        destroyOnClose={true}
        mask={true}
        maskClosable={false}
        onClose={onClose}
        placement="right"
        title="分配处理人"
        v-model:open={open.value}
        width={924}
      >
        {{
          extra: () => (
            <Button
              class="flex items-center justify-center"
              icon={(
                <Icon class="font-size-18px" component={<Close />} />
              )}
              onClick={onClose}
              type="text"
            />
          ),
          default: () => (
            <div class="resolver-content h-full flex flex-col">
              <DrawerHeader cl={props.data?.cl} user={props.data?.submitter} />
              <Form layout="vertical" model={form.value} ref={formRef} rules={rules}>
                <Form.Item label={<span class="FO-Font-B14">请选择处理人</span>} name="resolvers">
                  <ForgeonUserSelector
                    loading={userListLoading.value}
                    multiple
                    onReset={resetUserList}
                    onSearch={(params) => {
                      queryUser(params, {});
                    }}
                    options={userListOptions.value}
                    placeholder="请输入用户姓名/昵称/邮箱/拼音"
                    showAvatar
                    v-model:value={form.value.resolvers}
                  />
                </Form.Item>
              </Form>
            </div>
          ),
          footer: () => (
            <div class="flex items-center justify-end gap-12px">
              <Button class="btn-fill-default" onClick={onClose} type="text">取消</Button>
              <Button class="btn-fill-primary" disabled={form.value.resolvers.length <= 0} onClick={onSubmit} type="primary">提交</Button>
            </div>
          ),
        }}
      </Drawer>
    );
  },
});

export {
  AssignResolver,
};
