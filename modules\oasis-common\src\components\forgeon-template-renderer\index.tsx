import { type PropType, computed, defineComponent } from 'vue';
import type { JSX } from 'vue/jsx-runtime';

type Part = { type: 'text'; value: string } | { type: 'field'; key: string; value: any };

/**
 * 解析模板字符串，将其分解为文本和字段部分
 */
function parseTemplate(
  template: string,
  data: Record<string, any>,
  delimiter: [string, string],
): Part[] {
  const [start, end] = delimiter.map((d) => d.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&'));
  const pattern = `${start}(\\w+)${end}`;
  const regex = new RegExp(pattern, 'g');

  const parts: Part[] = [];
  let lastIndex = 0;

  // 使用 matchAll 提取所有匹配项
  const matches = [...template.matchAll(regex)];

  matches.forEach((match) => {
    const [placeholder, key] = match;
    const index = match.index ?? 0;

    // 前面的静态文本
    if (index > lastIndex) {
      parts.push({
        type: 'text',
        value: template.slice(lastIndex, index),
      });
    }

    parts.push({
      type: 'field',
      key,
      value: data?.[key] ?? placeholder,
    });

    lastIndex = index + placeholder.length;
  });

  // 最后剩余的文本
  if (lastIndex < template.length) {
    parts.push({
      type: 'text',
      value: template.slice(lastIndex),
    });
  }

  return parts;
}

const ForgeonTemplateRenderer = defineComponent({
  props: {
    template: {
      type: String as PropType<string>,
      required: true,
    },
    data: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
    delimiter: {
      type: Object as PropType<[string, string]>,
      default: () => ['%{', '}'],
    },
    customRender: {
      type: Object as PropType<Record<string, (val: any) => JSX.Element>>,
      default: () => ({}),
    },
  },
  setup(props, { slots }) {
    const parts = computed(() => parseTemplate(props.template, props.data, props.delimiter));
    return () => {
      if (slots.default) {
        return slots.default({ parts: parts.value });
      }

      return (
        <span>
          {parts.value.map((part) => {
            if (part.type === 'text') {
              return part.value;
            }

            const renderer = props.customRender?.[part.key];
            return renderer ? renderer(part.value) : part.value;
          })}
        </span>
      );
    };
  },
});

export {
  ForgeonTemplateRenderer,
};
