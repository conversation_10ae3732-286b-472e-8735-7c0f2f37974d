<template>
  <Tab
    :isSettingsPage="isSettingsPage" :counts="counts"
    :curTab="isSettingsPage ? PlatformEnterPoint.DeviceManagement : curActiveTab"
  >
    <template #action>
      <div v-if="!isSettingsPage" class="flex items-center gap-2">
        <BasicButton class="!px-6px" @click="() => handleOpenHistoryDrawer()">
          <div class="flex items-center gap-1">
            <Icon :icon="HistoryOutlinedIcon" class="c-FO-Brand-Primary-Default" />
            借用历史
          </div>
        </BasicButton>
        <BasicButton v-if="state.isDeviceAdmin" class="!px-6px" @click="() => handleDeptDeviceManage()">
          <div class="flex items-center gap-1">
            <Icon :icon="CategoryManagementIcon" class="c-FO-Brand-Primary-Default" />
            部门设备管理
          </div>
        </BasicButton>
        <BasicButton class="!px-6px" @click="() => handleSocClick(0)">
          <div class="flex items-center gap-1">
            <Icon icon="mtl-equipment-ladder|svg" :size="20" />
            设备天梯
          </div>
        </BasicButton>
      </div>
    </template>
    <div class="bg-FO-Container-Fill1">
      <div class="px-8 py-4">
        <SearchForm
          ref="searchFormRef" v-model:selectedTags="state.selectedTags" :tags="state.tags"
          :isSetting="isSettingsPage" :deptList="state.deptList" :chipsetList="chipsetList"
          :deviceBrandList="deviceBrandList" @search="handleSearchInfoChange" @tagClose="handleTagClose"
          @clearTags="handleClearTags" @keywordChange="handleKeywordChange" @filterChange="handleFilterChange"
        />
      </div>
    </div>
    <div class="flex items-center justify-between px-8 py-4">
      <div class="flex items-center gap-2">
        <div class="c-FO-Content-Text2">
          共 {{ totalNum }} 台设备
        </div>
        <ATag
          v-if="!isSettingsPage && todoTotal"
          v-tippy="{ content: onlyTodoDevice ? '点击恢复筛选' : '点击查看待处理事项', placement: 'bottom' }"
          :color="onlyTodoDevice ? '#ED7070' : 'error'" class="cursor-pointer text-14px"
          @click="() => handleOnlyTodoDevice()"
        >
          {{ todoTotal }} 条待处理
        </ATag>
      </div>
      <div class="flex items-center justify-end gap-2">
        <FilesSortChange
          v-if="viewType === 'table' && !viewSettingInit" :viewSetting="viewSetting"
          :nowTab="isSettingsPage ? 'deviceManage' : curActiveTab" @changeColumn="filesSortChange"
        />
        <template v-if="!isSettingsPage && curActiveTab === 'allDevice'">
          <ADropdown :trigger="['click']" :getPopupContainer="getPopupContainer">
            <BasicButton>
              筛选模板
              <Icon icon="ant-design:down-outlined" class="text-FO-Container-Stroke2" :size="14" />
            </BasicButton>
            <template #overlay>
              <AMenu @click="(e) => handleFilterTemplate(e.key)">
                <AMenuItem key="add">
                  <Icon icon="ant-design:plus-outlined" />
                  保存当前筛选条件
                </AMenuItem>
                <AMenuDivider v-if="state.filterTemplateList?.length" />
                <AMenuItem v-for="item in state.filterTemplateList" :key="item.ID">
                  <div class="flex items-center justify-between gap-1">
                    <div class="flex items-center gap-1">
                      <span>{{ item.name }}</span>
                      <ATag v-if="item.isDefault" :bordered="false">
                        默认
                      </ATag>
                    </div>
                    <APopconfirm title="确定要删除该筛选模板吗？" @confirm="() => handleDeleteFilterTemplate(item.ID)">
                      <BasicButton type="text" size="small" @click.stop>
                        <Icon icon="ion:trash-outline" />
                      </BasicButton>
                    </APopconfirm>
                  </div>
                </AMenuItem>
              </AMenu>
            </template>
          </ADropdown>
        </template>
        <AInputGroup class="w-auto" compact>
          <a-select
            v-model:value="sortTypeName" :options="sortTypeOptions" class="w-150px b-r-transparent z-99!"
            :getPopupContainer="getPopupContainer" @change="() => handleSortReload()"
          />
          <BasicButton class="px-2" @click="handleSortDirection">
            <ArrowDown v-if="sortTypeDirection === 'desc'" v-tippy="'降序'" />
            <ArrowUp v-else v-tippy="'升序'" />
          </BasicButton>
        </AInputGroup>
        <template v-if="isSettingsPage">
          <ADropdownButton @click="handleDeviceCheck">
            <span class="c-FO-Content-Text1">设备检测</span>
            <template #overlay>
              <AMenu>
                <AMenuItem key="export" @click="() => handleExport()">
                  <div v-tippy="{ content: '导出所有设备(无视筛选)', placement: 'left' }" class="flex items-center gap-1">
                    <Icon icon="ion:download-outline" />
                    导出
                  </div>
                </AMenuItem>
                <AMenuItem key="add" @click="() => handleCreate()">
                  <Icon icon="ion:add-outline" />
                  新增设备
                </AMenuItem>
              </AMenu>
            </template>
          </ADropdownButton>
        </template>
        <ARadioGroup v-model:value="viewType" class="view-type-class" @change="viewTypeChange">
          <ATooltip>
            <template #title>
              表格视图
            </template>
            <ARadioButton value="table">
              <Icon :icon="TableFileIcon" />
            </ARadioButton>
          </ATooltip>

          <ATooltip>
            <template #title>
              卡片视图
            </template>
            <ARadioButton value="card">
              <Icon :icon="ViewGridCardIcon" />
            </ARadioButton>
          </ATooltip>
        </ARadioGroup>
      </div>
    </div>

    <div v-if="viewType === 'card'" class="grid grid-cols-1 gap-4 px-8 2xl:grid-cols-4 lg:grid-cols-2 xl:grid-cols-3">
      <ACard
        v-for="item in deviceList" :key="item.ID"
        class="cursor-pointer rd-4px bg-FO-Container-Fill1 [&>.ant-card-body]:(h-full p-4)" hoverable
        @click="() => handleCardClick(item)"
      >
        <div class="h-full flex flex-col justify-between gap-2" :class="{ '!text-gray-400': !item.online }">
          <div class="w-full flex items-center justify-between gap-1">
            <EllipsisText
              class="min-w-0 flex-1 font-bold" :class="{
                'c-FO-Functional-Error1-Default': item.mobileType === 2 && item.online,
              }"
            >
              {{ item.deviceName + (item.mobileType === 2 ? '(开发机)' : '') }}
            </EllipsisText>
            <div v-if="isSettingsPage" class="w-fit">
              <Icon icon="carbon:dot-mark" :class="item.online ? '!c-FO-Functional-Success1-Default' : '!text-gray-400'" />
              <span v-if="item.online">上线</span>
              <span v-else class="text-gray-400">离线</span>
            </div>
            <div
              v-else-if="item?.fsmState !== DeviceFsmStateEnum.FREE && item.currentUserID"
              v-tippy="getShowUserNickName(item) ? `点击联系：${getShowUserNickName(item)}` : undefined"
              class="w-fit flex cursor-pointer items-center gap-1 rd-full bg-FO-Container-Fill2 px-3 py-1"
              @click.stop="() => handleUserClick(item?.currentUserID)"
            >
              <img :src="getShowUserAvatar(item)" class="h-24px w-24px rounded-full">
              <span class="whitespace-nowrap c-FO-Brand-Primary-Default">{{ getShowUserStatus(item) }}</span>
            </div>
          </div>
          <div class="flex items-center">
            <div class="mr-3 h-160px w-120px flex items-center justify-center">
              <AImage
                class="max-h-160px max-w-120px object-contain"
                :src="holderUrl(item?.picURL)" :preview="false"
              />
            </div>
            <div class="min-w-0 flex-1">
              <div class="flex flex-col gap-1">
                <div v-if="!isSettingsPage && item.chipset?.socPK">
                  跑分: <span
                    v-tippy="'点击前往查看天梯图'" class="w-fit cursor-pointer c-FO-Brand-Primary-Default"
                    @click.stop="() => handleSocClick(item.chipsetID!)"
                  >{{ item.chipset?.socPK }}</span>
                </div>

                <div v-for="text in cardContentList" v-show="!text.cardHidden" :key="text.value" class="flex items-center">
                  <span class="whitespace-nowrap">
                    {{ text.label }}
                  </span>
                  <EllipsisText class="c-FO-Content-Text2">
                    {{ getCardContent(text, item) }}
                  </EllipsisText>
                </div>
              </div>
            </div>
          </div>
          <div class="w-full flex items-center justify-between gap-4">
            <div class="flex items-center gap-1">
              <span
                v-if="!isSettingsPage && item.fsmState && ![DeviceFsmStateEnum.FREE].includes(item.fsmState) && item.latestApplyLog?.returnTime"
              >
                <ATag v-if="isOverdue(item.latestApplyLog?.returnTime)" color="red" :bordered="false">
                  逾期
                </ATag>
                <span :class="isOverdue(item.latestApplyLog?.returnTime) ? 'c-FO-Functional-Error1-Default' : 'c-FO-Content-Text2'">
                  {{ formatReturnTime(item.latestApplyLog?.returnTime || 0, true) }}
                </span>
              </span>
              <Icon
                v-if="!isSettingsPage && item.fsmState !== DeviceFsmStateEnum.FREE && (hasAllPermission(item) || userStore.isITAssetManagement)"
                v-tippy="'更改预计归还时间'" :icon="EditIcon"
                class="cursor-pointer c-FO-Content-Text2 hover:c-FO-Content-Text3"
                @click.stop="() => handleApply(item, ApplyTypeEnum.CHANGE_RETURN_TIME)"
              />
              <Icon
                v-if="!isSettingsPage && item.fsmState !== DeviceFsmStateEnum.FREE && item.currentUserID !== userStore.getUserInfo.ID && !isCurDeviceAdmin(item)"
                v-tippy="item.isSubscribed ? '取消订阅“空闲提醒”' : '订阅“空闲提醒”'" icon="material-symbols:bookmark-star-rounded"
                :class="item.isSubscribed ? 'c-FO-Brand-Primary-Default hover:c-FO-Brand-Primary-Hover' : 'c-FO-Content-Text2 hover:c-FO-Content-Text3'"
                class="cursor-pointer"
                @click.stop="() => handleSubscribe(item)"
              />
            </div>
            <div class="flex items-center gap-2">
              <div v-if="isSettingsPage" class="flex items-center gap-2">
                <APopconfirm title="确定要删除该设备吗？" @confirm="() => handleDelete(item)">
                  <BasicButton type="error" :disabled="item.online" size="small" @click.stop>
                    <span v-if="!item.online">删除</span>
                    <ATooltip v-else>
                      <template #title>
                        仅支持删除离线设备
                      </template>
                      删除
                    </ATooltip>
                  </BasicButton>
                </APopconfirm>
              </div>
              <template v-else>
                <template v-if="canBorrow(item)">
                  <BasicButton
                    v-if="isCurDeviceAdmin(item)"
                    class="w-90px !b-FO-Brand-Primary-Default !c-FO-Brand-Primary-Default hover:!bg-FO-Container-Fill2"
                    @click.stop="() => handleApply(item, ApplyTypeEnum.DIRECT_BORROW)"
                  >
                    借出
                  </BasicButton>
                  <BasicButton
                    v-else-if="!isUnavailable(item)" class="w-90px" type="primary"
                    @click.stop="() => handleApply(item, ApplyTypeEnum.BORROW_APPLY)"
                  >
                    借用
                  </BasicButton>
                </template>
                <template v-if="canReturn(item)">
                  <BasicButton
                    v-if="isCurDeviceAdmin(item)"
                    class="w-90px !b-FO-Functional-Success1-Default !c-FO-Functional-Success1-Default hover:!bg-FO-Container-Fill2"
                    @click.stop="() => handleDirectReturn(item)"
                  >
                    确认归还
                  </BasicButton>
                  <BasicButton
                    v-else class="w-90px" type="success"
                    @click.stop="() => handleApply(item, ApplyTypeEnum.RETURN_APPLY)"
                  >
                    归还申请
                  </BasicButton>
                </template>
                <APopconfirm
                  v-if="canPickUp(item)" title="确认你已借出该设备吗？" placement="topRight" okText="确认"
                  @confirm="() => handlePickUp(item)"
                >
                  <BasicButton class="w-90px" type="success" @click.stop>
                    确认借出
                  </BasicButton>
                </APopconfirm>
                <BasicButton
                  v-if="canAuditReturn(item) || canAudit(item)" class="w-90px" type="error"
                  @click.stop="() => handleApply(item, canAuditReturn(item) ? ApplyTypeEnum.RETURN_AUDIT : ApplyTypeEnum.BORROW_AUDIT)"
                >
                  审批
                </BasicButton>
              </template>
            </div>
          </div>
        </div>
      </ACard>
    </div>
    <div v-else class="mx-8">
      <ApplyTableView
        v-if="showTotal" ref="applyTableViewRef" class="relative"
        :nowTab="isSettingsPage ? 'deviceManage' : curActiveTab" :deptList="state.deptList" :onlyTodoDevice="onlyTodoDevice"
        :deviceList="tableDeviceData" :tablePage="tablePage" :viewSetting="viewSetting" :isSettingsPage="isSettingsPage"
        @handleSubscribeEmit="(record) => handleSubscribe(record)" @handlePickUpEmit="(record) => handlePickUp(record)"
        @handleDirectReturnEmit="(record) => handleDirectReturn(record)"
        @handleApplyEmit="(record, type) => handleApply(record, type)" @accessLevelSuccess="getDevices" @filedSuccess="getDevices"
        @deleteSuccess="getDevices" @clickDeviceName="(item) => handleCardClick(item)"
        @clickSocPK="(item) => handleSocClick(item)" @pageChangeEmit="pageChange" @changeCheckedKeys="changeCheckedKeys"
      />
    </div>
    <div v-if="checkedKeys.length" class="absolute bottom-0 h-80px w-100% flex items-center justify-between b-t-1px b-t-FO-Container-Stroke1 bg-FO-Container-Fill1 px-20px">
      <div>
        已选择{{ checkedKeys.length }}台设备
      </div>
      <div class="flex gap-4">
        <BasicButton @click="() => initAccessLevel()">
          取消
        </BasicButton>
        <APopover v-if="!isSettingsPage" v-model:open="batchVisible" trigger="click" placement="bottom">
          <template #content>
            <div class="flex flex-col">
              <AButton type="text" class="b-0 b-rd-0 bg-transparent! hover:bg-FO-Container-Fill2!" @click="changeAccessLevelcheckedKey">
                修改流通级别
              </AButton>

              <AButton type="text" class="b-0 b-rd-0 bg-transparent! hover:bg-FO-Container-Fill2!" :disabled="!canBatchBorrow" @click="handleBatchBorrow">
                批量借出设备
              </AButton>

              <AButton type="text" class="b-0 b-rd-0 bg-transparent! hover:bg-FO-Container-Fill2!" :disabled="!canBatchReturn" @click="handleBatchReturn">
                批量确认归还
              </AButton>
            </div>
          </template>
          <BasicButton type="primary">
            批量操作
          </BasicButton>
        </APopover>
        <BasicButton v-else type="primary" @click="changeAccessLevelcheckedKey">
          修改流通级别
        </BasicButton>
      </div>
    </div>
    <template v-if="!isInit">
      <div v-if="showTotal === 0" class="h-500px flex items-center justify-center">
        <AEmpty>
          <template #description>
            <template v-if="!isSettingsPage && curActiveTab === 'allDevice'">
              <template v-if="onlyTodoDevice || state.urlQuery?.assetID">
                <div class="text-14px text-gray-400">
                  {{ state.urlQuery?.assetID ? '无法查看该设备' : '暂无待处理设备' }}
                </div>
                <BasicButton type="primary" class="mt-4" @click="() => handleAllDevice()">
                  查看全部设备
                </BasicButton>
              </template>
              <template v-else>
                <div class="text-14px text-gray-400">
                  暂无符合条件的设备，你可前往"资产领用"申请需要的设备
                </div>
                <BasicButton type="primary" class="mt-4" @click="() => handleAssetApply()">
                  前往申请
                </BasicButton>
              </template>
            </template>
            <template v-else>
              <div class="text-14px text-gray-400">
                暂无设备
              </div>
            </template>
          </template>
        </AEmpty>
      </div>
      <div v-if="showTotal !== 0 && viewType === 'card'" class="w-full flex justify-end px-8 py-4">
        <APagination
          v-model:current="page" v-model:pageSize="pageSize" size="small" :total="showTotal" showSizeChanger
          :pageSizeOptions="['12', '24', '36', '48']" @change="handlePageChange"
        />
      </div>
    </template>
    <DeviceModal
      :chipsetList="chipsetList" :brandList="deviceBrandList" @register="registerDeviceModal"
      @success="handleSuccess" @chipsetChange="getChipsetList" @brandChange="getDeviceBrandList"
      @deviceDetail="deviceDetail"
    />
    <DetailModal @register="registerDetailModal" @success="handleSuccess" @deviceEdit="handleEdit" />
    <ApplyModal @register="registerApplyModal" @success="handleSuccess" />
    <AuditModal @register="registerAuditModal" @success="handleSuccess" />
    <DeviceCheckModal @register="registerDeviceCheckModal" @success="handleSuccess" />
    <EquipmentRankingsModal @register="registerEquipmentRankingsModal" @success="handleSuccess" />
    <AddFilterTemplateModal @register="registerFilterTemplateModal" @success="getFilterTemplates" />
    <DeptDeviceModal @register="registerDeptDeviceModal" @success="handleSuccess" />
    <AccessLevelModal @register="registerAccessLevelModal" @success="initAccessLevel" />
    <SuccessModal @register="registerSuccessModal" />
    <HistoryDrawerHolder />
    <BatchApplyModalHolder />
  </Tab>
</template>

<script lang="ts" setup>
import { cardContentList, sortTypeOptions } from './device.data';
import { ApplyTypeEnum } from './apply.data';
import { UseHistoryTypeEnum } from '../uesHistory.data';
import type { DeptAssetStateType } from './types';
import type { DeviceCounts, DeviceListItem, DevicesViewModel, DeviceTagItem, FilterTemplateListItem } from '/@/api/page/model/deptAssetModel';
import { DeviceApplyStateEnum, DeviceCategoryTypeEnum, DeviceFsmStateEnum, DeviceSortTypeEnum } from '/@/api/page/model/deptAssetModel';
import EquipmentRankingsModal from './EquipmentRankingsModal.vue';
import SuccessModal from './detail/SuccessModal.vue';
import AccessLevelModal from './detail/AccessLevelModal.vue';
import HistoryDrawer from '../components/HistoryDrawer.vue';
import { BasicButton } from '/@/components/Button';
import {
  Button as AButton,
  Card as ACard,
  Dropdown as ADropdown,
  DropdownButton as ADropdownButton,
  Empty as AEmpty,
  Image as AImage,
  InputGroup as AInputGroup,
  Menu as AMenu,
  MenuDivider as AMenuDivider,
  MenuItem as AMenuItem,
  Pagination as APagination,
  Popconfirm as APopconfirm,
  Popover as APopover,
  RadioButton as ARadioButton,
  RadioGroup as ARadioGroup,
  Tag as ATag,
  Tooltip as ATooltip,
} from 'ant-design-vue';
import { computed, onMounted, reactive, ref, toRefs } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import DeviceModal from './DeviceModal.vue';
import ApplyModal from './detail/ApplyModal.vue';
import BatchApplyModal from './detail/BatchApplyModal.vue';
import DetailModal from './detail/DetailModal.vue';
import AuditModal from './detail/AuditModal.vue';
import DeviceCheckModal from './settings/DeviceCheckModal.vue';
import AddFilterTemplateModal from './AddFilterTemplateModal.vue';
import FilesSortChange from './FilesSortChange.vue';
import ApplyTableView from './ApplyTableView.vue';
import { useResizeObserver } from '@vueuse/core';
import {
  confirmBorrowDevice,
  deleteDevice,
  deleteDeviceFilterTemplate,
  devicesApplyBatchDirect,
  exportDeviceList,
  getAssetDevicesViewApi,
  getChipsetsListByPage,
  getDeviceBrandsListByPage,
  getDeviceFilterTemplateList,
  getDeviceList,
  getDeviceTagList,
  isDeviceAdmin,
  setAssetDevicesViewApi,
  subscribeDevice,
  unsubscribeDevice,
} from '/@/api/page/deptAsset';
import { getDeptList } from '/@/api/page/system';
import { Icon } from '/@/components/Icon';
import { useModal } from '/@/components/Modal';
import { downloadByUrl } from '/@/utils/file/download';
import { useMessage } from '/@/hooks/web/useMessage';
import { getAllPaginationList } from '/@/hooks/web/usePagination';
import { useDeptAssetApply } from './hook';
import Tab from './components/Tab.vue';
import { HYPERGRYPH_AMS_URL } from '/@/settings/siteSetting';
import { ArrowDown, ArrowUp } from '@icon-park/vue-next';
import SearchForm from './components/SearchForm.vue';
import { ForgeonTitleMap, PlatformEnterPoint } from '@hg-tech/oasis-common';
import { EllipsisText } from '/@/components/EllipsisText';
import { useUserStore } from '/@/store/modules/user';
import { getPopupContainer } from '/@/utils';
import CategoryManagementIcon from '@iconify-icons/icon-park-outline/category-management';
import EditIcon from '@iconify-icons/icon-park-outline/edit';
import HistoryOutlinedIcon from '@iconify-icons/ant-design/history-outlined';
import ViewGridCardIcon from '@iconify-icons/icon-park-outline/view-grid-card';
import TableFileIcon from '@iconify-icons/icon-park-outline/table-file';
import DeptDeviceModal from './settings/DeptDeviceModal.vue';
import { useDeviceHolderImage } from '../getDeviceImg';
import type { BasicColumn } from '/@/components/Table';
import { sendEvent } from '../../../service/tracker';
import { useModalShow } from '@hg-tech/utils-vue';

const props = withDefaults(defineProps<{
  /** 是否是配置页 */
  isSettingsPage: boolean;
}>(), {
  isSettingsPage: false,
});

const route = useRoute();
const { replace } = useRouter();
const userStore = useUserStore();
const { holderUrl } = useDeviceHolderImage();
const [HistoryDrawerHolder, showHistoryDrawer] = useModalShow(HistoryDrawer);
const { isCurDeviceAdmin, isUnavailable, canBorrow, canPickUp, canReturn, canAudit, canAuditReturn, formatReturnTime, isOverdue, getShowUserNickName, getShowUserAvatar, getShowUserStatus, handleUserClick, getCardContent, hasAllPermission, getDevicePlatform } = useDeptAssetApply();
const applyTableViewRef = ref();
const viewType = ref('table');
const viewSetting = ref<DevicesViewModel>({});
const tableDeviceData = ref();
const checkedKeys = ref<number[]>([]);
const batchVisible = ref(false);
const viewSettingInit = ref(true);
const tablePage = ref({
  current: 1,
  pageSize: 20,
  total: 0,
});
const curActiveTab = ref<'allDevice' | 'myDevice' | 'deviceManage' | 'myOccupy' | undefined>(
  (route.query.tab as 'allDevice' | 'myDevice' | 'deviceManage' | 'myOccupy' | undefined) || 'allDevice',
);
const state: DeptAssetStateType = reactive({
  isInit: true,
  tags: {},
  counts: {} as DeviceCounts,
  onlyTodoDevice: !!Number(route.query.todo),
  sortTypeName: (curActiveTab.value === 'myDevice' || curActiveTab.value === 'myOccupy') ? 'returnTime' : 'inTime',
  sortTypeDirection: 'desc',
  selectedTags: {},
  deptList: [],
  chipsetList: [],
  deviceBrandList: [],
  urlQuery: {
    assetID: Number(route.query.editId) || undefined,
    chipsetID: Number(route.query.chipsetID) || undefined,
    recycle: Number(route.query.recycle) || undefined,
  },
  page: 1,
  pageSize: 12,
  totalNum: 0,
  todoTotal: 0,
  deviceList: [],
  filterTemplateList: [],
  isDeviceAdmin: false,
});

const {
  isInit,
  page,
  pageSize,
  counts,
  onlyTodoDevice,
  sortTypeName,
  sortTypeDirection,
  chipsetList,
  deviceBrandList,
  totalNum,
  todoTotal,
  deviceList,
} = toRefs(state);

const showTotal = computed(() => (onlyTodoDevice.value ? todoTotal.value : totalNum.value));

const deviceProductType = computed(() => {
  return props.isSettingsPage ? ForgeonTitleMap[PlatformEnterPoint.DeptAssetsManagement] : ForgeonTitleMap[PlatformEnterPoint.DeptAssetApplyManagement];
});

const searchFormRef = ref();

const [registerDeviceModal, { openModal: openDeviceModal }] = useModal();
const [registerApplyModal, { openModal: openApplyModal }] = useModal();
const [registerDetailModal, { openModal: openDetailModal }] = useModal();
const [registerAuditModal, { openModal: openAuditModal }] = useModal();
const [registerDeviceCheckModal, { openModal: openDeviceCheckModal }] = useModal();
const [registerFilterTemplateModal, { openModal: openFilterTemplateModal }] = useModal();
const [registerEquipmentRankingsModal, { openModal: openEquipmentRankingsModal }] = useModal();
const [registerDeptDeviceModal, { openModal: openDeptDeviceModal }] = useModal();
const [registerAccessLevelModal, { openModal: openAccessLevelModal }] = useModal();
const [registerSuccessModal, { openModal: openSuccessModal }] = useModal();
const [BatchApplyModalHolder, showBatchApplyModal] = useModalShow(BatchApplyModal);

const canBatchBorrow = computed(() => {
  return checkedKeys.value.every((id) => {
    const device = tableDeviceData.value.find((d) => d.ID === id);
    return device?.fsmState === DeviceFsmStateEnum.FREE;
  });
});

const canBatchReturn = computed(() => {
  return checkedKeys.value.every((id) => {
    const device = tableDeviceData.value.find((d) => d.ID === id);
    return [DeviceFsmStateEnum.RETURNING, DeviceFsmStateEnum.USING].includes(device?.fsmState);
  });
});

/** 获取部门列表 */
async function getDepts() {
  const { list } = await getDeptList();
  state.deptList = list || [];
}

function changeCheckedKeys(checkedKeysParam: number[]) {
  checkedKeys.value = checkedKeysParam;
}

async function getChipsetList() {
  const { list } = await getAllPaginationList(getChipsetsListByPage);
  state.chipsetList = list || [];
}

async function getDeviceBrandList() {
  const { list } = await getAllPaginationList(getDeviceBrandsListByPage);
  state.deviceBrandList = list || [];
}

/** 获取筛选标签列表 */
async function getTags() {
  const { tags, counts } = await getDeviceTagList({ online: props.isSettingsPage ? undefined : true });
  state.tags = {
    ...tags,
  };
  state.counts = counts;
}

function changeAccessLevelcheckedKey() {
  batchOperationTracking('修改流通级别');
  batchVisible.value = false;
  openAccessLevelModal(true, {
    isRowSelection: true,
    ids: checkedKeys.value,
    deptList: state.deptList,
  });
}
async function handleBatchBorrow() {
  batchOperationTracking('批量借出设备');
  batchVisible.value = false;
  await showBatchApplyModal(
    { title: '批量借出设备', deviceList: tableDeviceData.value.filter((item) => checkedKeys.value.includes(item.ID)), sentReq: (formValue: { proposerID: number; returnTime: number }) => {
      const res = devicesApplyBatchDirect({
        deviceIds: checkedKeys.value,
        proposerID: formValue.proposerID,
        returnTime: formValue.returnTime,
      });
      return res;
    } },
  );
  openSuccessModal(true, { type: ApplyTypeEnum.DIRECT_BORROW, title: '批量借出设备' });
  handleSuccess();
}
function handleBatchReturn() {
  batchOperationTracking('批量确认归还');
  batchVisible.value = false;
  openAuditModal(true, {
    isDirectReturn: true,
    deviceIds: checkedKeys.value,
    isBatch: true,
  });
}

function initAccessLevel() {
  applyTableViewRef.value.checkedKeys = [];
  applyTableViewRef.value.selectedRowKeysItem = [];
  getDevices();
}

/** 获取是否是设备管理员 */
async function getIsDeviceAdmin() {
  const { isAdmin } = await isDeviceAdmin();
  state.isDeviceAdmin = !!isAdmin;
}

/** 分页 */
function pageChange(page: { current: number; pageSize: number }) {
  tablePage.value.current = page.current;
  tablePage.value.pageSize = page.pageSize;
  getDevices();
}

/** 获取设备列表 */
async function getDevices() {
  const { list, total, todoTotal } = await getDeviceList({
    page: viewType.value === 'table' ? tablePage.value.current : state.page,
    pageSize: viewType.value === 'table' ? tablePage.value.pageSize : state.pageSize,
    ...state.selectedTags,
    assetID: state.urlQuery.assetID,
    keyword: searchFormRef.value?.getFieldsValue().keyword,
    online: props.isSettingsPage ? undefined : true,
    myOccupy: curActiveTab.value === 'myOccupy',
    myDevice: curActiveTab.value === 'myDevice',
    sortType: getSortType(),
    onlyTodoDevice: state.onlyTodoDevice,
  });
  tablePage.value.total = onlyTodoDevice.value ? todoTotal : total;
  state.totalNum = total;
  state.todoTotal = todoTotal;
  state.deviceList = list || [];
  tableDeviceData.value = list || [];
  state.isInit = false;
}

async function viewTypeChange() {
  const { view } = await getAssetDevicesViewApi();
  viewSetting.value = JSON.parse(view || '{}');

  if (viewSetting.value) {
    if (!viewSetting.value.view) {
      viewSetting.value.view = {};
    }
    viewSetting.value.view[props.isSettingsPage ? 'deviceManage' : curActiveTab.value as 'allDevice' | 'myDevice' | 'myOccupy'] = viewType.value;
    await setAssetDevicesViewApi({ view: JSON.stringify(viewSetting.value) });
  }
  tablePage.value = {
    current: 1,
    pageSize: 20,
    total: 0,
  };
  state.page = 1;
  state.pageSize = 12;
  getDevices();
}

function getSortType() {
  if (state.sortTypeName === 'inTime') {
    return state.sortTypeDirection === 'desc' ? DeviceSortTypeEnum.IN_TIME_DESC : DeviceSortTypeEnum.IN_TIME_ASC;
  } else if (state.sortTypeName === 'score') {
    return state.sortTypeDirection === 'desc' ? DeviceSortTypeEnum.SCORE_DESC : DeviceSortTypeEnum.SCORE_ASC;
  } else if (state.sortTypeName === 'returnTime') {
    return state.sortTypeDirection === 'desc' ? DeviceSortTypeEnum.RETURNTIME_DESC : DeviceSortTypeEnum.RETURNTIME_ASC;
  } else if (state.sortTypeName === 'useDays') {
    return state.sortTypeDirection === 'desc' ? DeviceSortTypeEnum.USEDAYS_DESC : DeviceSortTypeEnum.USEDAYS_ASC;
  }
}
// 埋点设备借用-批量操作
function batchOperationTracking(menu_name: string) {
  sendEvent('apply_device_batch_action', {
    menu_name,
  });
}
function handleSortDirection() {
  state.sortTypeDirection = state.sortTypeDirection === 'desc' ? 'asc' : 'desc';
  sortReload();
}

function handleSortReload() {
  state.sortTypeDirection = getSortTypeDirection();
  sortReload();
}

async function sortReload() {
  const { view } = await getAssetDevicesViewApi();
  viewSetting.value = JSON.parse(view || '{}');
  if (!viewSetting.value) {
    viewSetting.value = {};
  }
  if (!viewSetting.value.sortType) {
    viewSetting.value.sortType = {};
  }
  viewSetting.value.sortType[props.isSettingsPage ? 'deviceManage' : curActiveTab.value as 'allDevice' | 'myDevice' | 'myOccupy'] = { name: state.sortTypeName, direction: state.sortTypeDirection };
  await setAssetDevicesViewApi({ view: JSON.stringify(viewSetting.value) });
  sendEvent('device_sort_switch', {
    device_product_type: deviceProductType.value,
    sort_option: sortTypeOptions.find((item) => item.value === state.sortTypeName)?.label || state.sortTypeName,
  });
  viewType.value === 'table' ? pageChange({ current: 1, pageSize: 20 }) : handleReload();
}
async function handleReload() {
  state.page = 1;
  await getDevices();
}

// 分页处理
function handlePageChange(p: number, size: number) {
  state.page = p;
  state.pageSize = size;
  handleSuccess();
}

function handleCreate() {
  openDeviceModal(true, {
    isUpdate: false,
    deptList: state.deptList,
  });
}

function handleEdit(record: DeviceListItem) {
  openDeviceModal(true, {
    deviceID: record.ID,
    isUpdate: true,
    deptList: state.deptList,
  });
}

function deviceDetail(record?: DeviceListItem) {
  if (record) {
    handleDetail(record);
  }
}

async function handleDelete(record: DeviceListItem) {
  if (!record.ID) {
    return;
  }

  await deleteDevice(record.ID);
  await handleSuccess();
}

function handleDetail(record: DeviceListItem) {
  openDetailModal(true, {
    deviceID: record.ID,
    brandList: state.deviceBrandList,
    deptList: state.deptList,
    isSettingsPage: props.isSettingsPage,
  });
}

function handleDeptDeviceManage() {
  openDeptDeviceModal(true, {
    deptList: state.deptList,
    userID: userStore.getUserInfo?.ID,
  });
}

async function handleOpenHistoryDrawer() {
  sendEvent('device_borrow_history_click', {
    device_product_type: deviceProductType.value,
  });
  await showHistoryDrawer({
    type: UseHistoryTypeEnum.apply,
  });
}

/** 处理申请、审核、归还 */
function handleApply(record: DeviceListItem, type: ApplyTypeEnum) {
  openApplyModal(true, {
    deviceID: record.ID,
    type,
  });
}

function handleDirectReturn(record: DeviceListItem) {
  openAuditModal(true, {
    deviceID: record.ID,
    isDirectReturn: true,
  });
}

async function handlePickUp(record: DeviceListItem) {
  if (!record.latestApplyLog?.ID) {
    return;
  }

  await confirmBorrowDevice({
    deviceID: record.ID,
    isBorrow: true,
    state: DeviceApplyStateEnum.APPROVED,
  }, record.latestApplyLog.ID);
  await handleSuccess();
}

/** 处理卡片点击 */
function handleCardClick(record: DeviceListItem) {
  sendEvent('device_card_click', {
    device_name: record.deviceName,
    device_code: record.assetNo,
    device_product_type: deviceProductType.value,
    device_platform: getDevicePlatform(record),
  });
  if (props.isSettingsPage) {
    handleEdit(record);
  } else {
    handleDetail(record);
  }
}

function handleAllDevice() {
  if (route.query.todo) {
    replace({ query: { ...route.query, todo: undefined } });
  } else if (route.query.editId) {
    replace({ query: { ...route.query, editId: undefined } });
  } else {
    state.onlyTodoDevice = false;
    state.urlQuery = {};
    handleReload();
  }
}

async function handleSuccess() {
  applyTableViewRef.value.checkedKeys = [];
  applyTableViewRef.value.selectedRowKeysItem = [];
  await getTags();
  await getDevices();
  // 待处理设备不存在则去除url参数
  if (state.onlyTodoDevice && !state.todoTotal) {
    handleAllDevice();
  }
}

async function init() {
  // 初始加载数据
  getIsDeviceAdmin();
  await Promise.all([
    getTags(),
    getFilterTemplates(),
    getDepts(),
    getChipsetList(),
    getDeviceBrandList(),
  ]);
}

async function handleOnlyTodoDevice() {
  state.onlyTodoDevice = !state.onlyTodoDevice;
  sendEvent('apply_device_filter_todo');
  await getTags();
  handleReload();
  pageChange({ current: 1, pageSize: 20 });
}

function getDefaultViewType() {
  if (props.isSettingsPage || curActiveTab.value === 'myDevice') {
    return 'table';
  }
  return 'card';
}

function getDefaultSortName() {
  if (curActiveTab.value === 'myDevice' || curActiveTab.value === 'myOccupy') {
    return 'returnTime';
  }
  return 'inTime';
}
function getSortTypeDirection() {
  if (state.sortTypeName === 'returnTime') {
    return 'asc';
  }
  return 'desc';
}

useResizeObserver(() => searchFormRef.value, () => {
  applyTableViewRef.value?.redoHeightFn();
});

/**
 * 格式化筛选模板
 * @param template 筛选模板
 * @returns 格式化后的筛选模板
 */
function formatTemplate(template?: FilterTemplateListItem['template']) {
  const formatted: { filter_type: string; filter_option: string }[] = [];

  // 如果 template 为空，返回空数组
  if (!template) {
    return formatted;
  }

  for (const [key, value] of Object.entries(template)) {
    const filter_type = searchFormRef.value?.getLabel(key);
    if (!filter_type) {
      continue;
    }
    if (Array.isArray(value)) {
      // 如果是数组，option转换为分号分隔的字符串
      formatted.push({ filter_type, filter_option: value.map((item) => searchFormRef.value?.formatTagValue(key, item)).join(';') });
    } else {
      // 如果不是数组，option保持原值
      formatted.push({ filter_type, filter_option: searchFormRef.value?.formatTagValue(key, value) });
    }
  }

  return formatted;
}
function getChipsetTreeByChipsetIdList(selectedTemplate: FilterTemplateListItem['template']) {
  if (!selectedTemplate) {
    return;
  }
  selectedTemplate.chipsetTree = [];
  if (selectedTemplate?.chipsetIdList) {
    selectedTemplate?.chipsetIdList?.forEach((id: number) => {
      selectedTemplate?.chipsetTree.push([getCpuBrandById(id), id]);
    });
  }
  if (selectedTemplate?.chipsetBrandIDList) {
    selectedTemplate?.chipsetBrandIDList.forEach((item: number) => selectedTemplate.chipsetTree.push([item]));
  }
  return selectedTemplate;
}

onMounted(async () => {
  state.isInit = true;
  const { view } = await getAssetDevicesViewApi();

  viewSetting.value = JSON.parse(view || '{}');

  viewSettingInit.value = false;

  const viewKey = props.isSettingsPage ? 'deviceManage' : (curActiveTab.value as 'allDevice' | 'myDevice' | 'myOccupy');
  viewType.value = viewSetting.value?.view?.[viewKey] || getDefaultViewType();
  state.sortTypeName = viewSetting.value?.sortType?.[viewKey]?.name || getDefaultSortName();
  state.sortTypeDirection = viewSetting.value?.sortType?.[viewKey]?.direction || getSortTypeDirection();
  await init();
  const defaultTemplate = state.filterTemplateList.find((item) => item.isDefault);

  if (state.onlyTodoDevice) {
    sendEvent('apply_device_filter_todo');
  }
  if (!props.isSettingsPage && !state.urlQuery.assetID && curActiveTab.value === 'allDevice' && (state.urlQuery?.chipsetID || defaultTemplate)) {
    let template;
    if (state.urlQuery?.chipsetID) {
      template = {
        chipsetIdList: [state.urlQuery.chipsetID],
      };
    } else if (defaultTemplate) {
      template = defaultTemplate.template;
    }
    sendEvent('apply_device_filter_template', {
      apply_device_filter_template_info: formatTemplate(getChipsetTreeByChipsetIdList(template)),
    });
    searchFormRef.value?.setFieldsValue(getChipsetTreeByChipsetIdList(template));
  } else {
    await getDevices();
    await handleAfterFetch(state.deviceList);
  }
});

/** 判断url内设备id打开编辑抽屉 */
async function handleAfterFetch(list: DeviceListItem[]) {
  if (state.urlQuery?.assetID) {
    const findItem = list.find((e) => e.ID === state.urlQuery?.assetID);

    if (findItem) {
      handleCardClick(findItem);
    }
  }

  if (state.urlQuery?.recycle) {
    handleDeptDeviceManage();
  }
}

function handleSocClick(chipsetID: number) {
  sendEvent('device_ranking_panel_click', {
    device_product_type: deviceProductType.value,
  });
  openEquipmentRankingsModal(true, {
    socId: chipsetID,
  });
}

async function handleExport() {
  const { path } = await exportDeviceList();
  if (path) {
    const { createMessage } = useMessage();
    downloadByUrl({ url: path });
    createMessage.success('导出成功');
  }
}

/** 处理订阅 */
async function handleSubscribe(item: DeviceListItem) {
  if (!item.ID || item.isSubscribing) {
    return;
  }
  const { createMessage } = useMessage();
  item.isSubscribing = true;
  if (item.isSubscribed) {
    const res = await unsubscribeDevice(item.ID, DeviceCategoryTypeEnum.Common);
    if (res?.code !== 7) {
      createMessage.success('已取消订阅');
    }
  } else {
    const res = await subscribeDevice(item.ID, DeviceCategoryTypeEnum.Common);
    if (res.code !== 7) {
      createMessage.success('订阅成功');
    }
  }
  sendEvent('device_idle_alert_click', {
    device_product_type: deviceProductType.value,
    device_name: item.deviceName,
    device_code: item.assetNo,
    device_platform: getDevicePlatform(item),
  });
  await handleSuccess();
}

function handleDeviceCheck() {
  openDeviceCheckModal(true, {
    deptList: state.deptList,
  });
}

/** 处理筛选 */
async function handleSearchInfoChange() {
  // 获取表单值并解构出关键词
  const { keyword, ...filterValues } = searchFormRef.value?.getFieldsValue();
  const chipsetTree: number[][] = filterValues.chipsetTree;
  // 处理筛选标签
  state.selectedTags = Object.entries(filterValues).filter((item) => item[0] !== 'chipsetTree').reduce((tags, [key, value]) => {
    // 如果值存在且不为空数组则添加到标签中
    const hasValue = Array.isArray(value) ? value.length > 0 : Boolean(value);
    if (hasValue) {
      tags[key] = Array.isArray(value) ? value : [value];
    }
    return tags;
  }, {} as Record<string, any[]>);

  const chipsetIdList = chipsetTree?.filter((item) => {
    return item.length === 2;
  }).map((item) => item[1]);
  const chipsetBrandIDList = chipsetTree?.filter((item) => {
    return item.length === 1;
  }).map((item) => item[0]);
  if (chipsetIdList && chipsetIdList.length) {
    state.selectedTags.chipsetIdList = chipsetIdList;
  } else {
    delete state.selectedTags.chipsetIdList;
  }
  if (chipsetBrandIDList && chipsetBrandIDList.length) {
    state.selectedTags.chipsetBrandIDList = chipsetBrandIDList;
  } else {
    delete state.selectedTags.chipsetBrandIDList;
  }
  // 重置 URL 查询参数
  state.urlQuery = {};
  // 重置待处理设备筛选
  state.onlyTodoDevice = false;

  // 重新获取设备列表
  pageChange({ current: 1, pageSize: 20 });
  await handleReload();
}

function handleKeywordChange(value: string) {
  if (!value) {
    return;
  }
  sendEvent('device_search', {
    device_product_type: deviceProductType.value,
    search_keyword: value ?? '',
  });
}

function handleFilterChange(key: string, value: any[]) {
  if (!value?.length) {
    return;
  }
  sendEvent('device_filter', {
    device_product_type: deviceProductType.value,
    filter_type: key,
    filter_option: value ?? [],
  });
}
function getCpuBrandById(id) {
  return state.chipsetList.find((item) => item.ID === id)?.brand?.ID;
}

/** 处理标签关闭 */
async function handleTagClose(key: keyof DeviceTagItem, value: any) {
  const newTags = { ...state.selectedTags } as Record<string, any[]>;
  const values = newTags[key];
  if (Array.isArray(values)) {
    newTags[key] = values.filter((v) => v !== value);
    if (newTags[key]?.length === 0) {
      delete newTags[key];
    }
  } else {
    delete newTags[key];
  }
  if (key === 'chipsetIdList') {
    newTags[key] = newTags[key]?.map((id) => {
      return [getCpuBrandById(id), id];
    });
    const chipsetTree: number[][] = [];
    newTags.chipsetBrandIDList?.forEach((item) => chipsetTree.push([item]));
    newTags[key]?.forEach((item) => chipsetTree.push(item));
    searchFormRef.value?.setFieldsValue({ chipsetTree });
    newTags.chipsetIdList = newTags.chipsetIdList?.map((item) => item[1]);
  } else if (key === 'chipsetBrandIDList') {
    newTags.chipsetIdList = newTags.chipsetIdList?.map((id) => {
      return [getCpuBrandById(id), id];
    });
    const chipsetTree: number[][] = [];
    newTags[key]?.forEach((item) => chipsetTree.push([item]));
    newTags.chipsetIdList?.forEach((item) => chipsetTree.push(item));
    // 给CPU输入框赋值
    searchFormRef.value?.setFieldsValue({ chipsetTree });
    newTags.chipsetIdList = newTags.chipsetIdList?.map((item) => item[1]);
  } else {
    searchFormRef.value?.setFieldsValue({ [key]: newTags[key] });
  }

  state.selectedTags = newTags;
  await handleReload();
}

/** 处理清除所有标签 */
async function handleClearTags() {
  if (route.query?.chipsetID) {
    replace({ query: { ...route.query, chipsetID: undefined } });
    return;
  }
  state.selectedTags = {};
  await searchFormRef.value?.resetFields();
  handleReload();
}

/** 前往资产管理 */
function handleAssetApply() {
  window.open(HYPERGRYPH_AMS_URL, '_blank');
}

/** 获取筛选模板列表 */
async function getFilterTemplates() {
  const { list } = await getAllPaginationList(getDeviceFilterTemplateList);
  state.filterTemplateList = list || [];
}

/** 处理筛选模板相关操作 */
async function handleFilterTemplate(key: string | number) {
  const { createMessage } = useMessage();

  if (key === 'add') {
    if (searchFormRef.value?.getFieldsValue().keyword || Object.keys(state.selectedTags).length) {
      const template = {
        ...state.selectedTags,
        keyword: searchFormRef.value?.getFieldsValue().keyword,
      };
      openFilterTemplateModal(true, {
        template,
      });
    } else {
      createMessage.warning('请先设置筛选条件');
    }
  } else {
    await handleClearTags();
    const selectedTemplate = state.filterTemplateList.find((item) => item.ID === key)?.template;
    sendEvent('apply_device_filter_template', {
      apply_device_filter_template_info: formatTemplate(getChipsetTreeByChipsetIdList(selectedTemplate)),
    });
    await searchFormRef.value?.setFieldsValue(getChipsetTreeByChipsetIdList(selectedTemplate));

    createMessage.success('切换成功');
  }
}

/** 删除筛选模板 */
async function handleDeleteFilterTemplate(id?: number) {
  if (!id) {
    return;
  }

  await deleteDeviceFilterTemplate(id);
  await getFilterTemplates();
}
function filesSortChange(PlainOptions: BasicColumn[]) {
  applyTableViewRef.value.changeFilesColumns(PlainOptions);
}
</script>

<style lang="less" scoped>
.view-type-class {
  :deep(.ant-radio-button-wrapper-checked) {
    background: @FO-Brand-Primary-Default !important;
    border-color: @FO-Brand-Primary-Default !important;
    color: #fff !important;
  }
}
</style>
