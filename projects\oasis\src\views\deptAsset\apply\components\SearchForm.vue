<template>
  <div>
    <BasicForm
      :schemas="showSearchFormSchema"
      @register="registerForm"
      @submit="() => emit('search', chipsetTree)"
      @fieldValueChange="handleFieldValueChange"
    />
    <div v-if="Object.keys(selectedTags).length > 0" class="flex flex-wrap items-center gap-1 py-4">
      <template v-for="(values, key) in selectedTags" :key="key">
        <template v-for="value in values" :key="value">
          <ATag
            v-tippy="getTagTitle(key)"
            closable
            @close="emit('tagClose', key as keyof DeviceTagItem, value)"
          >
            {{ formatTagValue(key, value) }}
          </ATag>
        </template>
      </template>
      <ATag
        color="red"
        class="cursor-pointer"
        @click="emit('clearTags')"
      >
        <Icon icon="icon-park-outline:clear" size="12" />
        清除
      </ATag>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import type { FormSchema } from '/@/components/Form';
import { BasicForm, useForm } from '/@/components/Form';
import { Tag as ATag } from 'ant-design-vue';
import { Icon } from '/@/components/Icon';
import { userFilterOption } from '/@/hooks/system/useUserList';
import { assetTypeOptions, mobileStatusOptions, mobileTypeOptions, searchFormSchema } from '../device.data';
import { type ChipsetsListItem, type DeviceBrandListItem, type DeviceTagItem, DeviceFsmStateEnum } from '/@/api/page/model/deptAssetModel';
import type { DeptListItem } from '/@/api/page/model/systemModel';
import { useDeptAssetApply } from '../hook';
import { cloudDeviceStatusList } from '../../cloud/device.data';

export interface SearchFormProps {
  tags: Record<string, any>;
  deptList: DeptListItem[];
  chipsetList: ChipsetsListItem[];
  deviceBrandList: DeviceBrandListItem[];
  isCloud?: boolean;
  isSetting?: boolean;
}

const props = withDefaults(defineProps<SearchFormProps>(), {
  tags: () => ({}),
  deptList: () => [],
  chipsetList: () => [],
  deviceBrandList: () => [],
  isCloud: false,
  isSetting: false,
});

const emit = defineEmits<{
  search: [chipsetTree: number[][]];
  tagClose: [key: keyof DeviceTagItem, value: any];
  clearTags: [];
  fieldValueChange: [key: string, value: any];
  keywordChange: [value: string];
  filterChange: [key: string, value: any];
}>();

const selectedTags = defineModel<Record<string, any[]>>('selectedTags', { required: true });
const chipsetTree = ref<number[][]>([]);
const { formatDept, getUserById, getChipsetOptions, getChipsetBrandList } = useDeptAssetApply();
function setChipsetTree(value: number[][]) {
  chipsetTree.value = value;
}
const getChipsetBrandListComputed = computed(() => {
  const options = props.tags?.chipsetIdList || [];
  const list: ChipsetsListItem[] = [];
  props.chipsetList.forEach((e) => {
    if (options.includes(e.ID)) {
      list.push(e);
    }
  });
  return getChipsetBrandList(list);
});

/** 获取芯片名称 */
function formatchipsetIdList(ID: number) {
  const found = props.chipsetList.find((e) => e.ID === ID);
  return found?.brand?.nameCN && found?.socName ? `${found.brand?.nameCN} / ${found.socName}` : '';
}
/** 获取芯片品牌名称 */
function formatBrandOfChipset(ID: number) {
  const found = getChipsetBrandListComputed.value.find((e) => e.value === ID);
  return found?.label || '';
}
/** 获取品牌名称 */
function formatBrand(ID: number) {
  const found = props.deviceBrandList.find((e) => e.ID === ID);
  return found?.cnName ? `${found.cnName} (${found.name})` : (found?.name || '');
}

function formatUser(ID: number) {
  return getUserById(ID)?.displayName || '';
}
const chipsetTreeoptions = computed(() => {
  const options = props.tags?.chipsetIdList || [];
  const list: ChipsetsListItem[] = [];
  props.chipsetList.forEach((e) => {
    if (options.includes(e.ID)) {
      list.push(e);
    }
  });
  return getChipsetOptions(list) || [];
});

const showSearchFormSchema = computed<FormSchema[]>(() =>
  searchFormSchema.map((schema: FormSchema) => {
    const options = props.tags?.[schema.field] || [];
    const userComponentProps = {
      ...schema.componentProps,
      filterOption: userFilterOption,
      options: options.map((tag: any) => ({
        ...getUserById(tag),
        disabled: false,
        label: formatUser(tag),
        value: tag,
      })) || [],
    };
    switch (schema.field) {
      case 'currentUserIDList':
        schema.componentProps = userComponentProps;
        break;
      case 'ownerIDList':
        schema.componentProps = userComponentProps;
        schema.show = props.isSetting;
        break;
      case 'ownerAdminIDList':
        schema.componentProps = userComponentProps;
        schema.show = !props.isSetting;
        break;
      case 'deptIDList':
        schema.componentProps = {
          ...schema.componentProps,
          treeData: props.deptList || [],
        };
        break;
      case 'chipsetTree':
        schema.componentProps = {
          ...schema.componentProps,
          options: chipsetTreeoptions.value,
        };
        break;
      case 'brandIDList':
        schema.componentProps = {
          ...schema.componentProps,
          options: options.map((tag: any) => ({
            label: formatBrand(tag),
            value: tag,
          })) || [],
        };
        break;
      case 'fsmStateList':
        schema.show = !props.isCloud;
        schema.componentProps = {
          ...schema.componentProps,
          options: props.isSetting ? mobileStatusOptions : mobileStatusOptions.filter((item) => item.value !== DeviceFsmStateEnum.OFFLINE),
        };
        break;
      case 'status':
        schema.show = props.isCloud;

        break;
      case 'usageIDList':
        if (props.tags.usageList) {
          schema.componentProps = {
            ...schema.componentProps,
            options: props.tags.usageList.map((tag: any) => ({
              label: tag.label,
              value: tag.id,
            })) || [],
          };
        }
        break;
      case 'mobileTypeList':
      case 'assetTypeList':
        break;
      default:
        if (props.tags[schema.field]) {
          schema.componentProps = {
            ...schema.componentProps,
            options: options.map((tag: any) => ({
              label: tag,
              value: tag,
            })) || [],
          };
        }
        break;
    }
    return schema;
  }),
);

const [registerForm, { getFieldsValue, setFieldsValue, resetFields }] = useForm({
  labelWidth: 100,
  compact: true,
  actionColOptions: { flex: 1, span: 24 },
  baseColProps: { xl: 5, lg: 7, md: 23 },
  showAdvancedButton: true,
  submitOnChange: true,
  showSubmitButton: false,
  showResetButton: false,
  autoAdvancedLine: 1,
  alwaysShowLines: 1,
  autoFocusFirstItem: true,
  toggleTexts: ['更多筛选', '收起'],
});

/** 获取标签标题 */
function getTagTitle(key: string) {
  if (key === 'chipsetBrandIDList' || key === 'chipsetIdList') {
    return 'CPU';
  }
  const schema = (showSearchFormSchema.value as FormSchema[]).find((item) => item.field === key);
  return schema?.label || '';
}

/** 格式化标签值 */
function formatTagValue(key: string, value: any) {
  switch (key) {
    case 'currentUserIDList':
    case 'ownerIDList':
    case 'ownerAdminIDList':
      return formatUser(value);
    case 'chipsetIdList':
      return formatchipsetIdList(value);
    case 'chipsetBrandIDList':
      return formatBrandOfChipset(value);
    case 'deptIDList':
      return formatDept(value, props.deptList);
    case 'mobileTypeList':
      return mobileTypeOptions.find((opt) => opt.value === value)?.label || value;
    case 'assetTypeList':
      return assetTypeOptions.find((opt) => opt.value === value)?.label || value;
    case 'brandIDList':
      return formatBrand(value);
    case 'fsmStateList':
      return mobileStatusOptions.find((opt) => opt.value === value)?.label || value;
    case 'status':
      return cloudDeviceStatusList.find((opt) => opt.value === value)?.label || value;
    case 'usageIDList':
      return props.tags.usageList.find((opt: { id: number }) => opt.id === value)?.label || value;
    case 'chipsetTree':
      return formatChipsetTree(value);
    default:
      return value;
  }
}
function formatChipsetTree(value: number[]) {
  if (value.length === 1) {
    return formatBrandOfChipset(value[0]);
  }
  return formatchipsetIdList(value[1]);
}
/** 获取标签标题 */
function getLabel(key: string) {
  return showSearchFormSchema.value.find((item) => item.field === key)?.label as string;
}

/** 处理表单值变化 */
function handleFieldValueChange(key: string, value: any) {
  emit('fieldValueChange', key, value);

  if (key === 'keyword') {
    emit('keywordChange', value);
  } else {
    emit('filterChange', getLabel(key), value?.map((item: any) => formatTagValue(key, item)) || []);
  }
}

defineExpose({
  getFieldsValue,
  setFieldsValue,
  resetFields,
  getLabel,
  formatTagValue,
  setChipsetTree,
});
</script>
