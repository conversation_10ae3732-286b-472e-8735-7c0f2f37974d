import type { MtlInstallRecordListItem } from '../mtl/model/deviceModel';
import type { BaseItem, BasicFetchResult, BasicPageParams, BasicResult } from '/@/api/model/baseModel';
import type { DeptListItem, ProjectListItem } from '/@/api/page/model/systemModel';
import type { UserInfoModel } from '/@/api/sys/model/userModel';
import type { BasicColumn } from '/@/components/Table';

/** 设备状态 enum */
export enum DeviceFsmStateEnum {
  /** 空闲 */
  FREE = 1,
  /** 申请中 */
  APPLYING = 2,
  /** 领用中 */
  BORROWING = 3,
  /** 使用中 */
  USING = 4,
  /** 归还中 */
  RETURNING = 5,
  /** 离线 */
  OFFLINE = 7,
}

/** 资产用途 */
export enum DeviceUsageEnum {
  /** 个人使用 */
  PERSONAL = 1,
  /** 外包设备 */
  OUTSOURCE = 2,
  /** 部门公共设备 */
  DEPT_PUBLIC = 3,
}

/** 设备流通级别 enum */
export enum DeviceAccessLevelEnum {
  /** 公共 */
  PUBLIC = 1,
  /** 部门内流通 */
  DEPT = 2,
  /** 不可借用 */
  UNAVAILABLE = 3,
  /** 项目内流通 */
  PROJECT = 4,
}

/** 申请状态 enum */
export enum DeviceApplyStateEnum {
  /** 申请中 */
  APPLYING = 1,
  /** 已批准 */
  APPROVED = 2,
  /** 已拒绝 */
  REJECTED = 3,
}

/** 排序类型 enum */
export enum DeviceSortTypeEnum {
  /** 入库时间倒序 */
  IN_TIME_DESC = 1,
  /** 入库时间正序 */
  IN_TIME_ASC = 2,
  /** 跑分倒序 */
  SCORE_DESC = 3,
  /** 跑分正序 */
  SCORE_ASC = 4,
  /** 归还时间倒序 */
  RETURNTIME_DESC = 5,
  /** 归还时间正序 */
  RETURNTIME_ASC = 6,
  /** 使用天数倒序 */
  USEDAYS_DESC = 7,
  /** 使用天数正序 */
  USEDAYS_ASC = 8,
}

/** 资产类型 enum */
export enum AssetTypeEnum {
  /** 安卓 */
  ANDROID = 1,
  /** iOS */
  IOS = 2,
  /** PC */
  // PC = 3,
  /** 显示器 */
  // DISPLAY = 4,
  /** 其他 */
  // OTHER = 5,
}

/** 设备类型 enum */
export enum DeviceCategoryTypeEnum {
  /** 通用 */
  Common = 1,
  /** 云真机 */
  Cloud = 2,
}

/** 手机类型 enum */
export enum MobileTypeEnum {
  /** 商用机 */
  BUSINESS = 1,
  /** 开发机 */
  DEVELOP = 2,
}

/** 设备操作日志类型 enum */
export enum DeviceOptLogOperationTypeEnum {
  /** 申请使用 */
  APPLY = 1,
  /** 同意审批 */
  APPROVE = 2,
  /** 拒绝审批 */
  REJECT = 3,
  /** 确认领用 */
  CONFIRM_RECEIVE = 4,
  /** 申请归还 */
  APPLY_RETURN = 5,
  /** 确认归还 */
  CONFIRM_RETURN = 6,
  /** 拒绝归还 */
  REJECT_RETURN = 7,
  /** 直接借用 */
  DIRECT_BORROW = 8,
  /** 直接归还 */
  DIRECT_RETURN = 9,
  /** 更改归还日期 */
  CHANGE_RETURN_TIME = 10,
}

/** 设备类型 enum */
export enum DeviceTypeEnum {
  /** 安卓 */
  ANDROID = 1,
  /** iOS */
  IOS = 2,
  /** PC */
  PC = 3,
  /** 显示器 */
  DISPLAY = 4,
}

/** 流通部门列表 Model */
export interface AccessDeptListItem extends BaseItem {
  /** 部门ID */
  deptID?: number;
  /** 设备ID */
  deviceID?: number;
}
export interface AccessLevelList {
  accessLevel: number;
  deptID: number;
  deptIds: number[];
  projectIds: number[];
}
/** 流通项目列表 Model */
export interface AccessProjectListItem extends BaseItem {
  /** 项目ID */
  projectID?: number;
  /** 设备ID */
  deviceID?: number;
  /** 项目 */
  project?: ProjectListItem;
}

/** 设备列表 Model */
export interface DeviceListItem extends BaseItem {
  /** 设备唯一标识 */
  deviceUID?: string;
  /** 设备UDID */
  deviceUDID?: string;
  /** 设备名称 */
  deviceName?: string;
  /** 设备型号：iPhone11 Pro */
  deviceType?: string;
  /** 品牌 */
  brand?: string;
  brandID?: number;
  /** CPU芯片 */
  chipsetID?: number;
  chipset?: ChipsetsListItem;
  /** 资产分类:  1：安卓，2：iOS，3：PC，4：显示器 */
  assetType?: DeviceTypeEnum;
  /** 资产编号：YJ-IT-04354 */
  assetNo?: string;
  /** 手机类型：1：商用机，2：开发机 */
  mobileType?: number;
  /** 所属部门 */
  dept?: DeptListItem;
  /** 所属部门ID */
  deptID?: number;
  /** 所属者 */
  owner?: UserInfoModel;
  /** 所属者ID */
  ownerID?: number;
  /** 操作系统版本：14.4.2(18D70) */
  osVersion?: string;
  /** CPU类型 */
  cpuType?: string;
  /** GPU类型 */
  gpuType?: string;
  /** 机身内存(单位: GB) */
  memory?: number;
  /** 运行内存(单位: GB) */
  ram?: number;
  /** 屏幕分辨率: (宽 x 高) */
  resolution?: string;
  /** 最近申请信息 */
  latestApplyLog?: ApplyLogListItem;
  /** 最近使用信息 */
  latestUsageLog?: UsageLogListItem;
  /** 最近归还信息 */
  latestReturnLog?: ApplyLogListItem;
  /** 是否上线 */
  online?: boolean;
  /** 手机样图 */
  picURL?: string;
  /** 流通级别 */
  accessLevel?: DeviceAccessLevelEnum;
  /** 设备状态 */
  fsmState?: DeviceFsmStateEnum;
  /** 当前使用人ID */
  currentUserID?: number;
  /** 流通级别为部门内流通时，部门ID列表 */
  deptIds?: number[];
  /** 流通级别为部门内流通时，部门列表 */
  accessDepts?: AccessDeptListItem[];
  /** 流通级别为项目内流通时，项目列表 */
  accessProjects?: AccessProjectListItem[];
  /** 是否订阅 */
  isSubscribed?: boolean;
  /** 是否订阅云真机空闲提醒 */
  isCloudSubscribed?: boolean;
  /** 是否订阅中 */
  isSubscribing?: boolean;
  /** 定制版本 */
  romVersion?: string;
  /** 云真机设备ID */
  deviceId?: number;
  /** 云真机设备 状态 */
  status?: string;
  /** 云真机设备 使用人用户名 */
  user?: string;
  /** 云真机设备 剩余使用时长 */
  remainTime?: number;
  /** 资产用途 */
  usage?: UsageItem;
  /** 资产用途ID */
  usageID?: number;
  /** 部门管理员设备信息 */
  deptAdminDevice?: DeviceAdminDeviceListItem;
  /** 是否可以审批 */
  canApprove?: boolean;
  /** 备注 */
  remark?: string;
  deleted?: boolean;
}

/** id获取设备信息 接口参数 */
export interface DeviceListItemParams {
  assetDevice: DeviceListItem;
}

export type DeviceListPageParams = DeviceTagItem & BasicPageParams;
export type DeviceListGetResultModel = BasicFetchResult<DeviceListItem> & { todoTotal: number };

/** 设备筛选标签 */
export interface DeviceTagItem {
  /** 资产分类 */
  assetTypeList?: number[];
  /** 品牌 */
  brandIDList?: number[];
  /** CPU类型 */
  chipsetIdList?: number[];
  /** 部门id */
  deptIDList?: number[];
  /** 设备型号 */
  deviceTypeList?: string[];
  /** GPU类型 */
  gpuTypeList?: string[];
  /** 机身内存 */
  memList?: number[];
  /** 手机类型 */
  mobileTypeList?: number[];
  /** 操作系统 */
  osVersionList?: string[];
  /** 运行内存 */
  ramList?: number[];
  /** 分辨率 */
  resolutionList?: string[];
  /** 手机状态 */
  fsmStateList?: (DeviceFsmStateEnum)[];
  /** 我的设备 */
  myDevice?: boolean;
  /** 我占用的 */
  myOccupy?: boolean;
  /** 所有云真机 */
  allCloud?: boolean;
  /** 我占用的云真机 */
  myOccupyCloud?: boolean;
  /** 设备ID */
  assetID?: number;
  /** 是否上线 */
  online?: boolean;
  /** 图片地址 */
  picURL?: string;
  /** 搜索关键字 */
  keyword?: string;
  /** 排序类型 */
  sortType?: DeviceSortTypeEnum;
  /** 使用人id */
  currentUserIDList?: number[];
  /** 是否只显示待处理设备 */
  onlyTodoDevice?: boolean;
  /** 芯片品牌 */
  chipsetBrandIDList?: number[];
}

/** 设备统计 */
export interface DeviceCounts {
  /** 全部 */
  allDevice?: number;
  /** 我使用的 */
  myOccupy?: number;
  /** 我管理的 */
  myDevice?: number;
  /** 所有云真机 */
  allCloud?: number;
  /** 我占用的云真机 */
  myOccupyCloud?: number;
}

/** 设备筛选标签 接口返回 */
export interface DeviceTagGetResultModel {
  tags: DeviceTagItem;
  counts: DeviceCounts;
}

export interface ApplyLogListItem extends BaseItem {
  /** 设备id */
  deviceID?: number;
  /** 申请或归还状态 */
  state?: DeviceApplyStateEnum;
  /** 备注：批准，拒绝 */
  remark?: string;
  /** 审核人id */
  approveID?: number;
  /** 审核人 */
  approver?: UserInfoModel;
  /** 申请人id */
  proposerID?: number;
  /** 申请人 */
  proposer?: UserInfoModel;
  /** 归还时间 */
  returnTime?: number;
  /** 申请理由 */
  reason?: string;
  /** 是否已领用 */
  isBorrow?: boolean;
}

export type ApplyLogListPageParams = ApplyLogListItem & BasicPageParams;
export type ApplyLogListGetResultModel = BasicFetchResult<ApplyLogListItem>;

export interface ChangeReturnTimeParams {
  /** 设备ID */
  deviceID?: number;
  /** 审批状态 */
  state: DeviceApplyStateEnum;
  /** 更新后的归还时间 */
  returnTime: number;
  /** 更新原因 */
  updateReason?: string;
}

export enum UsageLogDeviceTypeEnum {
  /** 设备 */
  DEVICE = 1,
  /** 云真机 */
  CLOUD = 2,
}
export interface UsageLogListItem extends BaseItem {
  /** 设备id */
  deviceID?: number;
  /** 用户id */
  userID?: number;
  /** 用户信息 */
  user?: UserInfoModel;
  fsmState?: DeviceFsmStateEnum;
  /** 使用时间 */
  usedTime?: string;
  /** 申请id */
  applyLogID?: number;
  /** 设备类型 */
  deviceType?: UsageLogDeviceTypeEnum;
  /** 是否是最近记录 */
  isLatest?: boolean;
  userLog?: boolean;
  device?: DeviceListItem;

}

export type UsageLogListPageParams = UsageLogListItem & BasicPageParams & { filterUsers?: string };
export type UsageLogListGetResultModel = BasicFetchResult<UsageLogListItem>;

export interface DeviceBrandListItem extends BaseItem {
  name?: string;
  cnName?: string;
}

/** 品牌列表 Model */
export interface BrandsListItem extends BaseItem {
  name?: string;
  nameCN?: string;
  logoURL?: string;
  isOversea?: boolean;
}

export type BrandsListPageParams = BrandsListItem & BasicPageParams;
export type BrandsListGetResultModel = BasicFetchResult<BrandsListItem>;

/** 芯片列表 Model */
export interface ChipsetsListItem extends BaseItem {
  socName?: string;
  brandID?: number;
  brand?: BrandsListItem;
  socPK?: number;
  deviceCount?: number;
  /** 是否是云真机 */
  isCloud?: boolean;
}

export type ChipsetsListPageParams = ChipsetsListItem & BasicPageParams;
export type ChipsetsListGetResultModel = BasicFetchResult<ChipsetsListItem>;

/** 设备列表 Model */
export interface OverseasDeviceListItem extends BaseItem {
  brand?: string;
  chipset?: string;
  chipsetID?: number;
  name?: string;
  score?: number;
}

/** 海外芯片列表 Model */
export interface OverseasChipsetsListItem extends BaseItem {
  name?: string;
  gpu?: string;
  score?: number;
  cpuClock?: string;
  devices?: OverseasDeviceListItem[];
  brandID?: number;
}

export type OverseasChipsetsListPageParams = OverseasChipsetsListItem & BasicPageParams;
export type OverseasChipsetsListGetResultModel = BasicFetchResult<OverseasChipsetsListItem>;

/** 海外设备列表 Model */
export interface OverseasDevListItem extends BaseItem {
  name?: string;
  chipsetID?: number;
  chipset?: number;
  brand?: string;
  score?: number;
  chipset_id?: number;
}

export type OverseasDevListPageParams = OverseasDevListItem & BasicPageParams;
export type OverseasDevListGetResultModel = BasicFetchResult<OverseasDevListItem>;

/** 海外设备品牌列表 Model */
export interface OverseasDevBrandListGetResultModel {
  overseaDeviceBrands: string[];
}
export interface TagNameListItem {
  content?: string[];
  col?: number;
}
/**
 * 资产用途
 */
export interface UsageItem {
  label?: string;
  value?: string;
  id?: number;
}

/**
 * 检测设备异常信息
 */
export interface CheckDeviceExceptionItem {
  assetNo?: string;
  barcode?: string;
  deptID?: number;
  deviceID?: number;
  deviceName?: string;
  itUseDeptID?: number;
  itUserID?: number;
  ownerID?: number;
  specs?: string;
  useDepartmentCode?: string;
  userEmployeeNo?: string;
  assetUsage?: UsageItem;
  usage?: UsageItem;
  // 仅前端使用
  isUpdating?: boolean;
  isUpdated?: boolean;
  updatedErrorMsg?: string;
  isSelected?: boolean;
}

/**
 * 检测设备异常信息类型
 */
export enum CheckDeviceExceptionTypeEnum {
  /** 资产编号不匹配 */
  ASSET_NO_MISMATCH = 1,
  /** 借用人缺失 */
  USER_MISSING = 2,
  /** 设备名称不一致 */
  DEVICE_NAME_MISMATCH = 3,
  /** 归属人不一致 */
  OWNER_MISMATCH = 4,
  /** 部门不一致 */
  DEPT_MISMATCH = 5,
  /** 未注册设备 */
  DEVICE_NOT_REGISTERED = 6,
  /** 用途不一致 */
  USAGE_MISMATCH = 7,
}

/**
 * 检测设备异常信息 map
 */
export type CheckDeviceExceptionMap = {
  [key in CheckDeviceExceptionTypeEnum]?: CheckDeviceExceptionItem[];
};

/**
 * 检测设备异常信息结果
 */
export interface CheckDeviceResultModel {
  exceptions: CheckDeviceExceptionMap;
}

/**
 * 更新设备指定字段
 */
export interface UpdateDeviceFieldParams {
  /** 异常类型（3设备名称不一致；4归属人不一致；5部门不一致） */
  exceptionType?: CheckDeviceExceptionTypeEnum;
  /** 修改后的设备名 */
  deviceName?: string;
  /** 修改后的归属人 */
  ownerID?: number;
  /** 修改后的部门id */
  deptID?: number;
  /** 设备id */
  deviceID?: number;
  /** 修改后的用途 */
  usageID?: DeviceUsageEnum;
}

/**
 * 批量更新设备指定字段结果
 */
export interface BatchUpdateDeviceFieldResult extends Partial<BasicResult> {
  failCount: number;
  result: {
    isSuccess: boolean;
    errorMsg: string;
    exceptionType: CheckDeviceExceptionTypeEnum;
    deviceID: number;
    isUpdateAccessLevel: boolean;
  }[];
}

/**
 * 设备操作日志列表页参数
 */
export interface DeviceOptLogListPageParams extends BasicPageParams {
  /** 操作人ID数组 */
  operatorIDs?: number[];
  /** 操作类型数组 */
  operationTypes?: DeviceOptLogOperationTypeEnum[];
  /** 设备关键词搜索 */
  deviceKeyword?: string;
  /** 操作时间范围 */
  operateTimeRange?: number[];
  /** 设备ID数组 */
  deviceIDs?: number[];
}

/**
 * 设备操作日志列表 item
 */
export interface DeviceOptLogListItem extends BaseItem {
  /** 设备ID */
  deviceID?: number;
  /** 设备信息 */
  device?: DeviceListItem;
  /** 操作类型 */
  operationType?: DeviceOptLogOperationTypeEnum;
  /** 操作人ID */
  operatorID?: number;
  /** 操作人信息 */
  operator?: UserInfoModel;
  /** 申请记录ID */
  applyLogID?: number;
  /** 申请记录信息 */
  applyLog?: ApplyLogListItem;
}

export type DeviceOptLogListGetResultModel = BasicFetchResult<DeviceOptLogListItem>;

/**
 * 筛选模板列表 Model
 */
export interface FilterTemplateListItem extends BaseItem {
  name?: string;
  /** 是否是默认模板 */
  isDefault?: boolean;
  /** 筛选条件 */
  template?: Record<string, any>;
}

export type getDeviceFilterTemplateListResultModel = BasicFetchResult<FilterTemplateListItem>;

/**
 * 设备管理员列表 Model
 */
export interface DeviceAdminListItem extends BaseItem {
  userID: number;
  deptID: number;
  dept?: DeptListItem;
  user?: UserInfoModel;
  /** 最长借用天数 */
  maxDay?: number;
  /** 候补审批人 */
  subAdminIds?: number[];
  /** 候补审批人列表 */
  subAdmins?: UserInfoModel[];
}

export type DeviceAdminListGetResultModel = BasicFetchResult<DeviceAdminListItem>;

/**
 * 部门管理员设备列表页参数
 */
export interface DeviceAdminDeviceListParams extends BasicPageParams {
  /** 部门管理员记录ID（IT管理员打开管理员的设备窗口） */
  deptAdminID?: number;
  /** 管理员的用户ID */
  userID?: number;
  /** 设备ID */
  deviceID?: number;
  /** 回收状态：true-已被回收，false-未回收，不传返回所有设备 */
  isRecycled?: boolean;
  /** 资产编号或者设备名筛选 */
  keyword?: string;
}

/**
 * 部门管理员设备列表 item
 */
export interface DeviceAdminDeviceListItem extends BaseItem {
  deviceID: number;
  deptAdminID: number;
  isRecycled: boolean;
  recycledNotify: boolean;
  deptAdmin: DeviceAdminListItem;
  user: UserInfoModel;
}

/**
 * 批量回收或取消回收设备
 */
export interface BatchRecycleDeviceParams {
  /** 设备id列表 */
  deviceIds: number[];
  /** 是否回收 true回收 false取消回收 */
  recycle: boolean;
}
export interface UsageListOptions {
  id: number;
  label: string;
  value: string;
}
export interface UsageListGetResultModel {
  counts: number;
  options: UsageListOptions[];
}
export interface DevicesViewModel {
  sortType?: { allDevice?: { name: string; direction: 'desc' | 'asc' }; deviceManage?: { name: string; direction: 'desc' | 'asc' }; myOccupy?: { name: string; direction: 'desc' | 'asc' }; myDevice?: { name: string; direction: 'desc' | 'asc' } };
  view?: { allDevice?: string; deviceManage?: string; myOccupy?: string; myDevice?: string };
  allDevice?: BasicColumn[];
  myDevice?: BasicColumn[];
  deviceManage?: BasicColumn[];
  myOccupy?: BasicColumn[];
}

/**
 * 设备报障类型
 */
export interface FaultTypeListItem extends BaseItem {
  type: number;
  value: string;
}

export type FaultTypeListGetResultModel = BasicFetchResult<FaultTypeListItem>;

/**
 * 设备报障状态
 */
export enum FaultStatusEnum {
  /** 待处理 */
  PENDING = 1,
  /** 处理中 */
  PROCESSING = 2,
  /** 已关闭 */
  CLOSED = 3,
}

/**
 * 设备报障 model
 */
export interface FaultListItem extends BaseItem {
  /** 故障类型 */
  faultType: number;
  /** 安装ID */
  installId?: number;
  /** 故障描述 */
  desc: string;
  /** 截图 */
  screenshot?: string;
  /** 错误日志URL */
  errorLogUrl?: string;
  /** 设备ID */
  deviceId: number;
  /** 代理 */
  agent: string;
  /** 提交人 */
  creatorId: number;
  /** 状态 */
  status: FaultStatusEnum;
  /** 设备报障ID */
  id?: number;
  /** 安装记录 */
  installRecord?: MtlInstallRecordListItem;
}

export type FaultListGetResultModel = BasicFetchResult<FaultListItem>;
