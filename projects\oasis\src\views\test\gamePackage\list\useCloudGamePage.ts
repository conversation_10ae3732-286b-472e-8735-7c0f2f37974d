import { type MaybeRef, unref } from 'vue';
import type { GamePackagesListItem, GamePackagesVersionsListItem } from '../../../../api/page/model/testModel.js';
import { getCloudGameLoginLink } from '/@/api/page/test.js';
import { useUserStore } from '/@/store/modules/user.js';
import { sendEvent } from '../../../../service/tracker';
import { GamePackageCenterTrackSourceType, GamePackageCenterTrackTrigger } from '../../../../api/page/model/testModel.js';

export function useCloudGamePage(isOasis: MaybeRef) {
  /**
   * 打开云游戏
   * @param pkgId 游戏包id
   * @param pkgVersionId 版本id
   * @param isDetail 是否从详情页打开
   */
  async function openCloudGamePage(pkgId: GamePackagesListItem['ID'], pkgVersionId: GamePackagesVersionsListItem['ID'], isDetail = false) {
    const userStore = useUserStore();
    if (!userStore.getProjectId || !pkgId || !pkgVersionId) {
      return;
    }

    const { url: cloudGameLink } = await getCloudGameLoginLink(userStore.getProjectId, pkgId, pkgVersionId);

    if (unref(isOasis)) {
      window.open(`oasisdownload://open-browser?url=${encodeURIComponent(cloudGameLink)}`, '_blank');
    } else {
      window.open(cloudGameLink, '_blank');
    }
    sendEvent('game_package_center_enter_cloud_gaming_platform', {
      game_package_center_project_id: userStore.getProjectId,
      game_package_center_branch_id: pkgId,
      game_package_center_package_id: pkgVersionId,
      game_package_center_trigger_channel: unref(isOasis) ? GamePackageCenterTrackTrigger.Oasis : GamePackageCenterTrackTrigger.Web,
      game_package_center_source_type: isDetail ? GamePackageCenterTrackSourceType.DetailPage : GamePackageCenterTrackSourceType.ListItem,
    });
  }

  return {
    openCloudGamePage,
  };
}
